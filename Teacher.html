<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Dashboard</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/img/BINHSLogo.ico">

    <!-- Custom fonts for this template-->
    <link href="assets/admin/vendor/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css">
    <link
        href="https://fonts.googleapis.com/css?family=Nunito:200,200i,300,300i,400,400i,600,600i,700,700i,800,800i,900,900i"
        rel="stylesheet">

    <!-- Custom styles for this template-->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="assets/admin/css/sb-admin-2.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">

    <!-- Mobile Responsive Styles -->
    <link href="assets/css/responsive.css" rel="stylesheet">

    <!-- Custom CSS for lime green sidebar and mobile enhancements -->
    <style>
        /* Override sidebar background to lime green */
        .sidebar.bg-gradient-primary {
            background-color: #32CD32 !important;
            background-image: linear-gradient(180deg, #32CD32 10%, #228B22 100%) !important;
            background-size: cover;
        }

        /* Ensure sidebar brand and nav items have proper contrast */
        .sidebar.bg-gradient-primary .sidebar-brand {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link:hover,
        .sidebar.bg-gradient-primary .nav-item .nav-link:focus,
        .sidebar.bg-gradient-primary .nav-item.active .nav-link {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link i {
            color: rgba(255, 255, 255, 0.8) !important;
        }

        .sidebar.bg-gradient-primary .nav-item .nav-link:hover i,
        .sidebar.bg-gradient-primary .nav-item .nav-link:focus i,
        .sidebar.bg-gradient-primary .nav-item.active .nav-link i {
            color: #ffffff !important;
        }

        .sidebar.bg-gradient-primary .sidebar-divider {
            border-top: 1px solid rgba(255, 255, 255, 0.15) !important;
        }

        .sidebar.bg-gradient-primary #sidebarToggle {
            background-color: rgba(255, 255, 255, 0.2) !important;
        }

        .sidebar.bg-gradient-primary #sidebarToggle:hover {
            background-color: rgba(255, 255, 255, 0.3) !important;
        }

        /* Make the dashboard logo more noticeable */
        .sidebar-brand-icon {
            background: rgba(255, 255, 255, 0.95) !important;
            border-radius: 50% !important;
            padding: 8px !important;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3), 0 2px 6px rgba(0, 0, 0, 0.2) !important;
            border: 2px solid rgba(255, 255, 255, 0.8) !important;
            transition: all 0.3s ease !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
        }

        .sidebar-brand-icon img {
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2)) !important;
            transition: all 0.3s ease !important;
            border-radius: 50% !important;
        }

        /* Enhanced effects on hover */
        .sidebar-brand:hover .sidebar-brand-icon {
            background: rgba(255, 255, 255, 1) !important;
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4), 0 3px 8px rgba(0, 0, 0, 0.3) !important;
            border: 2px solid #ffffff !important;
            transform: scale(1.05) !important;
        }

        .sidebar-brand:hover .sidebar-brand-icon img {
            filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3)) !important;
        }

        /* Mobile sidebar enhancements */
        @media (max-width: 767.98px) {
            /* Ensure topbar toggle is visible on mobile */
            #sidebarToggleTop {
                display: block !important;
            }

            /* Hide sidebar toggle inside sidebar on mobile */
            .sidebar #sidebarToggle {
                display: none !important;
            }

            /* Mobile sidebar positioning and styling */
            .sidebar {
                z-index: 1050 !important;
                position: fixed !important;
                top: 0;
                left: -250px !important; /* Start hidden */
                width: 250px !important;
                height: 100vh;
                transition: left 0.3s ease-in-out;
                overflow-y: auto;
                background: #32CD32 !important;
                background-image: linear-gradient(180deg, #32CD32 10%, #228B22 100%) !important;
            }

            /* Show sidebar when NOT toggled (sb-admin-2.js removes 'toggled' class to show) */
            .sidebar:not(.toggled) {
                left: 0 !important;
            }

            /* Ensure sidebar is hidden when toggled */
            .sidebar.toggled {
                left: -250px !important;
            }

            /* Content wrapper adjustments for mobile */
            #content-wrapper {
                margin-left: 0 !important;
                width: 100% !important;
            }

            /* Mobile overlay for sidebar */
            .sidebar-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.5);
                z-index: 1040;
                display: none;
            }

            .sidebar-overlay.show {
                display: block;
            }

            /* Mobile topbar adjustments */
            .topbar {
                padding: 0.5rem 1rem !important;
            }

            .topbar .nav-link {
                padding: 0.75rem 0.5rem !important;
                min-height: 44px !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
            }

            /* Mobile Close Button Styling */
            .mobile-close-btn {
                position: absolute;
                top: 15px;
                right: 15px;
                width: 40px;
                height: 40px;
                background: rgba(50, 205, 50, 0.9) !important;
                border: 2px solid rgba(255, 255, 255, 0.3);
                border-radius: 8px;
                display: flex !important;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                z-index: 1060;
                transition: all 0.3s ease;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
            }

            .mobile-close-btn:hover {
                background: rgba(34, 139, 34, 0.95) !important;
                border-color: rgba(255, 255, 255, 0.5);
                transform: scale(1.05);
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            }

            .mobile-close-btn i {
                color: white !important;
                font-size: 18px;
                font-weight: bold;
            }

            /* Mobile dropdown improvements */
            .sidebar .collapse-inner {
                background: rgba(255, 255, 255, 0.95) !important;
                border: none !important;
                border-radius: 8px !important;
                margin: 0 10px !important;
                box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
                overflow: hidden !important;
            }

            .sidebar .collapse-header {
                background: rgba(50, 205, 50, 0.1) !important;
                color: #228B22 !important;
                font-weight: 600 !important;
                padding: 8px 15px !important;
                margin: 0 !important;
                font-size: 0.8rem !important;
                text-transform: uppercase !important;
                letter-spacing: 0.5px !important;
            }

            .sidebar .collapse-item {
                color: #5a5c69 !important;
                padding: 10px 15px !important;
                border: none !important;
                background: none !important;
                width: 100% !important;
                text-align: left !important;
                font-size: 0.9rem !important;
                transition: all 0.2s ease !important;
                border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
            }

            .sidebar .collapse-item:last-child {
                border-bottom: none !important;
            }

            .sidebar .collapse-item:hover {
                background: rgba(50, 205, 50, 0.1) !important;
                color: #228B22 !important;
                text-decoration: none !important;
            }

            /* Ensure dropdown doesn't overflow sidebar */
            .sidebar .collapse {
                max-width: 100% !important;
                overflow: hidden !important;
            }

            /* Mobile-specific dropdown styling */
            .sidebar .nav-link[data-toggle="collapse"] {
                position: relative;
            }

            .sidebar .nav-link[data-toggle="collapse"]:after {
                content: '\f107';
                font-family: 'Font Awesome 5 Free';
                font-weight: 900;
                position: absolute;
                right: 15px;
                top: 50%;
                transform: translateY(-50%);
                transition: transform 0.2s ease;
            }

            .sidebar .nav-link[data-toggle="collapse"][aria-expanded="true"]:after {
                transform: translateY(-50%) rotate(180deg);
            }
        }

        /* Hide close button on desktop */
        @media (min-width: 768px) {
            .mobile-close-btn {
                display: none !important;
            }
        }

        /* Sidebar Toggle Hover Style */
        #sidebarToggle .nav-link:hover {
            color: #105c1c !important;
        }

        /* Mobile sidebar toggle hover style */
        #sidebarToggleTop:hover {
            color: #105c1c !important;
        }
    </style>

</head>

<body id="page-top">

    <!-- Page Wrapper -->
    <div id="wrapper">

        <!-- Sidebar -->
        <ul class="navbar-nav bg-gradient-primary sidebar sidebar-dark accordion" id="accordionSidebar">

            <!-- Mobile Close Button -->
            <div class="mobile-close-btn d-block d-md-none" id="mobileSidebarClose">
                <i class="fas fa-chevron-left"></i>
            </div>

            <!-- Sidebar - Brand -->
            <a class="sidebar-brand d-flex align-items-center justify-content-center" href="#" id="dashboard-icon">
                <div class="sidebar-brand-icon">
                    <img src="assets/img/BINHSLogo.ico" alt="BINHS Logo" style="width: 30px; height: 30px;">
                </div>
                <div class="sidebar-brand-text mx-3">Dashboard</div>
            </a>

            <!-- Divider -->
            <hr class="sidebar-divider my-0">

            <!-- Nav Item - Dashboard -->
            <li class="nav-item active">
                <a class="nav-link" href="#" id="logout-btn">
                    <i class="fas fa-sign-out-alt"></i>
                    <span>Logout</span>
                </a>
            </li>

            <!-- Divider
            <hr class="sidebar-divider">-->

            <!-- Heading -->
            <!--<div class="sidebar-heading">
                Interface
            </div> -->

            <!-- Nav Item - Pages Collapse Menu <li class="nav-item">
                <a class="nav-link collapsed" href="#" data-toggle="collapse" data-target="#collapse"
                    aria-expanded="true" aria-controls="collapse">
                    <i class="fa-solid fa-user"></i>
                    <span>View Students</span>
                </a>
                <div id="collapse" class="collapse" aria-labelledby="headingTwo" data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Grades:</h6>
                        <a class="collapse-item" href="">Grade 11</a>
                        <a class="collapse-item" href="">Grade 12</a>
                    </div>
                </div>
            </li>-->




            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Nav Item - Utilities Collapse Menu -->
            <li class="nav-item">
                <a class="nav-link" href="#" id="manage-sections-btn">
                    <i class="fa-solid fa-chalkboard"></i>
                    <span>Classes</span>
                </a>
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <li class="nav-item">
                <a class="nav-link" href="#" id="manage-quizzes-btn">
                    <i class="fa-solid fa-file-pen"></i>
                    <span>Quizzes and Exams</span>
                </a>

            </li>



            <li class="nav-item">
                <a class="nav-link" href="#" id="manage-assessments-btn">
                    <i class="fas fa-pencil-alt"></i>
                    <span>Students Assessments</span>
                </a>

            </li>

            <li class="nav-item">
                <button class="nav-link" id="system-nav" style="background: none; border: none; color: rgba(255, 255, 255, 0.8);">
                    <i class="fa-solid fa-chalkboard"></i>
                    <span>My Classroom Materials</span>
                </button>
                <!--<div id="collapseUtilities" class="collapse" aria-labelledby="headingUtilities"
                    data-parent="#accordionSidebar">
                    <div class="bg-white py-2 collapse-inner rounded">
                        <h6 class="collapse-header">Custom Utilities:</h6>
                        <a class="collapse-item" href="utilities-color.html">Colors</a>
                        <a class="collapse-item" href="utilities-border.html">Borders</a>
                        <a class="collapse-item" href="utilities-animation.html">Animations</a>
                        <a class="collapse-item" href="utilities-other.html">Other</a>
                    </div>
                </div>-->
            </li>

            <!-- Divider -->
            <hr class="sidebar-divider">

            <!-- Heading -->
            <!--<div class="sidebar-heading">
                Addons
            </div>-->

            <!-- Nav Item - Charts <li class="nav-item">
                <a class="nav-link" href="charts.html">
                    <i class="fas fa-fw fa-chart-area"></i>
                    <span>Charts</span></a>
            </li>
-->

            <!-- Nav Item - Tables <li class="nav-item">
                <a class="nav-link" href="tables.html">
                    <i class="fas fa-fw fa-table"></i>
                    <span>Tables</span></a>
            </li>-->




            <!-- Sidebar Toggler (Sidebar) -->
            <div class="text-center d-none d-md-inline">
                <button class="rounded-circle border-0" id="sidebarToggle"></button>
            </div>

            <!-- Sidebar Message
             <div class="sidebar-card d-none d-lg-flex">
                <img class="sidebar-card-illustration mb-2" src="img/undraw_rocket.svg" alt="...">
                <p class="text-center mb-2"><strong>SB Admin Pro</strong> is packed with premium features, components, and more!</p>
                <a class="btn btn-success btn-sm" href="https://startbootstrap.com/theme/sb-admin-pro">Upgrade to Pro!</a>
            </div>-->


        </ul>
        <!-- End of Sidebar -->

        <!-- Content Wrapper -->
        <div id="content-wrapper" class="d-flex flex-column">

            <!-- Main Content -->
            <div id="content">

                <!-- Topbar -->
                <nav class="navbar navbar-expand navbar-light bg-white topbar mb-4 static-top shadow">

                    <!-- Sidebar Toggle (Topbar) -->
                    <li class="nav-item">
                        <a class="nav-link" href="#" id="sidebarToggleTop">
                            <i class="fas fa-bars"></i>
                        </a>
                    </li>

                    <!-- Topbar Search
                    <form
                        class="d-none d-sm-inline-block form-inline mr-auto ml-md-3 my-2 my-md-0 mw-100 navbar-search">
                        <div class="input-group">
                            <input type="text" class="form-control bg-light border-0 small" placeholder="Search for..."
                                aria-label="Search" aria-describedby="basic-addon2">
                            <div class="input-group-append">
                                <button class="btn btn-primary" type="button">
                                    <i class="fas fa-search fa-sm"></i>
                                </button>
                            </div>
                        </div>
                    </form> -->

                    <!-- Topbar Navbar -->
                    <ul class="navbar-nav ml-auto">



                        <!-- Nav Item - Alerts -->


                        <!-- Nav Item - Messages -->


                        <div class="topbar-divider d-none d-sm-block"></div>

                        <!-- Nav Item - User Information -->
                        <li class="nav-item dropdown no-arrow">
                            <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button"
                                data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                <span class="mr-2 d-none d-lg-inline text-gray-600 small">Teacher</span>
                                <i class="fas fa-cog fa-fw text-gray-400"></i>
                            </a>
                            <!-- Dropdown - User Information -->
                            <div class="dropdown-menu dropdown-menu-right shadow animated--grow-in"
                                aria-labelledby="userDropdown">
                                <div class="dropdown-header text-center">
                                    <div class="profile-info">
                                        <span id="profileName" class="font-weight-bold"></span>
                                        <div class="small text-gray-500" id="profileEmail"></div>
                                    </div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#profileModal">
                                    <i class="fas fa-user fa-sm fa-fw mr-2 text-gray-400"></i>
                                    View Profile
                                </a>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="#" data-toggle="modal" data-target="#logoutModal">
                                    <i class="fas fa-sign-out-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                    Logout
                                </a>
                            </div>
                        </li>

                    </ul>

                </nav>
                <!-- End of Topbar -->







                <!-- Begin Page Content -->
                <div class="container-fluid">

                    <!-- Page Heading -->
                    <div class="d-sm-flex align-items-center justify-content-between mb-4">
                        <h1 class="h3 mb-0 text-gray-800">Welcome Teacher!</h1>

                    </div>

                    <style>
                        .hidden {
                            display: none;
                        }

                        .clickable-classroom {
                            cursor: pointer;
                            transition: transform 0.2s ease, box-shadow 0.2s ease;
                        }

                        .clickable-classroom:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
                        }

                        /* Book Icon as Background */
                        .clickable-classroom::after {
                            content: "\f02d";
                            font-family: "Font Awesome 6 Free";
                            font-weight: 900;
                            position: absolute;
                            right: 15px;
                            top: 15px;
                            font-size: 60px;
                            color: rgba(0, 0, 0, 0.1);
                            pointer-events: none;
                        }

                        /* Three-dot menu */
                        .menu-btn {
                            position: absolute;
                            top: 10px;
                            right: 10px;
                            background: none;
                            border: none;
                            cursor: pointer;
                            font-size: 20px;
                        }

                        .card-menu {
                            position: absolute;
                            top: 30px;
                            right: 10px;
                            background: white;
                            border: 1px solid #ddd;
                            box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
                            padding: 5px;
                            border-radius: 5px;
                            z-index: 10;
                        }

                        .card-menu button {
                            display: block;
                            width: 100%;
                            border: none;
                            background: none;
                            padding: 5px;
                            cursor: pointer;
                        }

                        .card-menu button:hover {
                            background: #f0f0f0;
                        }

                        #teacher-add-classroom-card {
                            position: fixed;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            z-index: 1000;
                            background: white;
                            width: 380px;
                            max-height: 85vh;
                            overflow-y: auto;
                            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
                            padding: 15px;
                            border-radius: 8px;
                        }

                        #teacher-overlay {
                            position: fixed;
                            top: 0;
                            left: 0;
                            width: 100%;
                            height: 100%;
                            background: rgba(0, 0, 0, 0.5);
                            z-index: 999;
                        }

                        /* Style for edit input fields */
                        .edit-input {
                            width: 100%;
                            padding: 8px;
                            margin-bottom: 10px;
                            border: 1px solid #ccc;
                            border-radius: 4px;
                            font-size: 14px;
                            box-sizing: border-box;
                        }

                        /* Improved styles for classroom materials display */
                        .classroom-item {
                            transition: transform 0.2s ease, box-shadow 0.2s ease;
                        }

                        .classroom-item:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
                        }

                        .classroom-item .text-muted {
                            font-size: 0.85rem;
                            color: #6c757d !important;
                        }

                        .classroom-item .font-weight-bold {
                            color: #495057 !important;
                        }

                        .classroom-item .text-dark {
                            color: #343a40 !important;
                        }

                        /* Sort controls styling */
                        .sort-controls {
                            display: flex;
                            align-items: center;
                            gap: 10px;
                        }

                        .sort-controls label {
                            margin: 0;
                            font-size: 0.875rem;
                            color: #6c757d;
                            white-space: nowrap;
                        }

                        .sort-controls select {
                            min-width: 180px;
                            font-size: 0.875rem;
                        }

                        /* Group header styling */
                        .group-header {
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 12px 20px;
                            margin: 20px 0 15px 0;
                            border-radius: 8px;
                            font-weight: 600;
                            font-size: 1.1rem;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                            position: relative;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }

                        .group-header:hover {
                            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                            transform: translateY(-1px);
                            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                        }

                        .group-header:first-child {
                            margin-top: 0;
                        }

                        .group-header::before {
                            content: '';
                            position: absolute;
                            left: 0;
                            top: 0;
                            bottom: 0;
                            width: 4px;
                            background: rgba(255,255,255,0.3);
                            border-radius: 8px 0 0 8px;
                        }

                        .group-header .group-info {
                            display: flex;
                            align-items: center;
                            flex: 1;
                        }

                        .group-header .group-count {
                            background: rgba(255,255,255,0.2);
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 0.85rem;
                            margin-left: 10px;
                        }

                        .group-header .group-minimize-btn {
                            background: rgba(255,255,255,0.2);
                            border: none;
                            color: white;
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            font-size: 14px;
                            margin-left: 10px;
                        }

                        .group-header .group-minimize-btn:hover {
                            background: rgba(255,255,255,0.3);
                            transform: scale(1.1);
                        }

                        .group-header .group-minimize-btn:focus {
                            outline: none;
                            box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
                        }

                        .group-header.collapsed .group-minimize-btn i {
                            transform: rotate(-90deg);
                        }

                        .group-content {
                            margin-bottom: 30px;
                            transition: all 0.3s ease;
                            opacity: 1;
                            max-height: none;
                            display: flex;
                            flex-wrap: wrap;
                            width: 100%;
                        }

                        .group-content.collapsed {
                            max-height: 0;
                            margin-bottom: 0;
                            opacity: 0;
                            padding: 0;
                            overflow: hidden;
                        }

                        .group-content .classroom-item {
                            margin-left: 0;
                            border-left: 3px solid #667eea;
                        }

                        .group-content .col-xl-3,
                        .group-content .col-md-6 {
                            margin-bottom: 20px;
                            opacity: 1;
                            visibility: visible;
                        }

                        .group-content .card {
                            opacity: 1;
                            visibility: visible;
                        }

                        /* Ensure proper grid layout for classroom cards */
                        #teacher-classroom-container.row {
                            display: flex;
                            flex-wrap: wrap;
                            margin-right: -15px;
                            margin-left: -15px;
                        }

                        #teacher-classroom-container .col-xl-3,
                        #teacher-classroom-container .col-md-6 {
                            padding-right: 15px;
                            padding-left: 15px;
                            margin-bottom: 1.5rem;
                        }

                        /* Text overflow handling for classroom cards */
                        .classroom-card-text {
                            overflow: hidden;
                            word-wrap: break-word;
                            word-break: break-word;
                            hyphens: auto;
                        }

                        .classroom-card-text.truncate {
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            line-clamp: 2;
                            -webkit-box-orient: vertical;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        .classroom-card-text.truncate-single {
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                        }

                        /* Specific styles for different text elements */
                        .subject-text {
                            max-width: 100%;
                        }

                        .section-text {
                            max-width: 100%;
                        }

                        .details-text {
                            max-width: 100%;
                            max-height: 3em;
                            line-height: 1.5em;
                        }

                        /* Tooltip styles for showing full text */
                        .text-tooltip {
                            position: relative;
                            cursor: help;
                        }

                        .text-tooltip:hover::after {
                            content: attr(data-full-text);
                            position: absolute;
                            bottom: 100%;
                            left: 50%;
                            transform: translateX(-50%);
                            background: rgba(0, 0, 0, 0.9);
                            color: white;
                            padding: 8px 12px;
                            border-radius: 4px;
                            font-size: 0.875rem;
                            white-space: normal;
                            max-width: 300px;
                            word-wrap: break-word;
                            z-index: 1000;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
                        }

                        .text-tooltip:hover::before {
                            content: '';
                            position: absolute;
                            bottom: 100%;
                            left: 50%;
                            transform: translateX(-50%) translateY(100%);
                            border: 5px solid transparent;
                            border-top-color: rgba(0, 0, 0, 0.9);
                            z-index: 1000;
                        }

                        /* Ensure consistent card heights */
                        .card.h-100 {
                            min-height: 200px;
                            max-height: 300px;
                        }

                        .card-body {
                            display: flex;
                            flex-direction: column;
                            justify-content: space-between;
                        }

                        .col.mr-2 {
                            flex: 1;
                            min-width: 0; /* Allow flex item to shrink below content size */
                        }

                        /* Classroom notification styles */
                        .classroom-notifications {
                            background: rgba(255, 193, 7, 0.1);
                            border: 1px solid rgba(255, 193, 7, 0.3);
                            border-radius: 8px;
                            padding: 12px;
                        }

                        .notification-item {
                            transition: all 0.2s ease;
                            border: 1px solid #e3e6f0 !important;
                        }

                        .notification-item:hover {
                            transform: translateY(-1px);
                            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                        }

                        .notification-item.bg-warning-light {
                            background-color: rgba(255, 193, 7, 0.1) !important;
                            border-color: rgba(255, 193, 7, 0.3) !important;
                        }

                        /* Quiz classroom information styles */
                        .bg-warning-light {
                            background-color: rgba(255, 193, 7, 0.1) !important;
                        }

                        .border-left {
                            border-left: 3px solid !important;
                        }

                        .border-primary {
                            border-left-color: #007bff !important;
                        }

                        .border-warning {
                            border-left-color: #ffc107 !important;
                        }

                        .quiz-card .card-body .bg-light {
                            background-color: #f8f9fa !important;
                            border-radius: 0.375rem;
                        }

                        /* Multiple classroom display styles */
                        .quiz-card .classroom-separator {
                            border-top: 1px solid #dee2e6;
                            margin-top: 0.5rem;
                            padding-top: 0.5rem;
                        }

                        .quiz-card .classroom-count-badge {
                            font-size: 0.75rem;
                            padding: 0.25rem 0.5rem;
                        }

                        .badge-sm {
                            font-size: 0.7rem;
                            padding: 0.2rem 0.4rem;
                        }

                        .notifications-list {
                            max-height: 300px;
                            overflow-y: auto;
                        }

                        .notifications-list::-webkit-scrollbar {
                            width: 4px;
                        }

                        .notifications-list::-webkit-scrollbar-track {
                            background: #f1f1f1;
                            border-radius: 2px;
                        }

                        .notifications-list::-webkit-scrollbar-thumb {
                            background: #c1c1c1;
                            border-radius: 2px;
                        }

                        .notifications-list::-webkit-scrollbar-thumb:hover {
                            background: #a8a8a8;
                        }

                        /* Remove notification button styles */
                        .remove-teacher-notification-btn {
                            opacity: 0.7;
                            transition: all 0.2s ease;
                        }

                        .remove-teacher-notification-btn:hover {
                            opacity: 1;
                            transform: scale(1.1);
                        }

                        .notification-item:hover .remove-teacher-notification-btn {
                            opacity: 1;
                        }

                        /* Assessment card styles */
                        .assessment-card {
                            transition: transform 0.2s ease, box-shadow 0.2s ease;
                            border-left: 4px solid #4e73df;
                            position: relative;
                            overflow: hidden;
                        }

                        .assessment-card:hover {
                            transform: translateY(-2px);
                            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15) !important;
                        }

                        .assessment-card::after {
                            content: "\f19d";
                            font-family: "Font Awesome 6 Free";
                            font-weight: 900;
                            position: absolute;
                            right: 15px;
                            top: 15px;
                            font-size: 50px;
                            color: rgba(0, 0, 0, 0.08);
                            pointer-events: none;
                        }

                        .assessment-score {
                            font-size: 1.5rem;
                            font-weight: bold;
                        }

                        .assessment-percentage {
                            font-size: 1.1rem;
                            font-weight: 600;
                        }

                        .assessment-details {
                            font-size: 0.875rem;
                            color: #6c757d;
                        }

                        .score-excellent {
                            color: #28a745;
                        }

                        .score-good {
                            color: #17a2b8;
                        }

                        .score-average {
                            color: #ffc107;
                        }

                        .score-poor {
                            color: #dc3545;
                        }

                        .assessment-group-header {
                            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                            color: white;
                            padding: 12px 20px;
                            margin: 20px 0 15px 0;
                            border-radius: 8px;
                            font-weight: 600;
                            font-size: 1.1rem;
                            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                            position: relative;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            display: flex;
                            justify-content: space-between;
                            align-items: center;
                        }

                        .assessment-group-header:hover {
                            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
                            transform: translateY(-1px);
                            box-shadow: 0 4px 8px rgba(0,0,0,0.15);
                        }

                        .assessment-group-header:first-child {
                            margin-top: 0;
                        }

                        .assessment-group-header::before {
                            content: '';
                            position: absolute;
                            left: 0;
                            top: 0;
                            bottom: 0;
                            width: 4px;
                            background: rgba(255,255,255,0.3);
                            border-radius: 8px 0 0 8px;
                        }

                        .assessment-group-header .group-info {
                            display: flex;
                            align-items: center;
                            flex: 1;
                        }

                        .assessment-group-header .group-count {
                            background: rgba(255,255,255,0.2);
                            padding: 4px 8px;
                            border-radius: 12px;
                            font-size: 0.85rem;
                            margin-left: 10px;
                        }

                        .assessment-group-header .group-minimize-btn {
                            background: rgba(255,255,255,0.2);
                            border: none;
                            color: white;
                            width: 32px;
                            height: 32px;
                            border-radius: 50%;
                            display: flex;
                            align-items: center;
                            justify-content: center;
                            cursor: pointer;
                            transition: all 0.2s ease;
                            font-size: 14px;
                            margin-left: 10px;
                        }

                        .assessment-group-header .group-minimize-btn:hover {
                            background: rgba(255,255,255,0.3);
                            transform: scale(1.1);
                        }

                        .assessment-group-header .group-minimize-btn:focus {
                            outline: none;
                            box-shadow: 0 0 0 2px rgba(255,255,255,0.3);
                        }

                        .assessment-group-header.collapsed .group-minimize-btn i {
                            transform: rotate(-90deg);
                        }

                        .assessment-group-content {
                            margin-bottom: 30px;
                            transition: all 0.3s ease;
                            overflow: hidden;
                            opacity: 1;
                            max-height: none;
                        }

                        .assessment-group-content.collapsed {
                            max-height: 0;
                            margin-bottom: 0;
                            opacity: 0;
                            padding: 0;
                        }

                        .assessment-group-content .assessment-card {
                            margin-left: 0;
                        }

                        .assessment-group-content .col-xl-4 {
                            margin-bottom: 20px;
                        }

                        /* Enhanced file preview modal styles */
                        #filePreviewModal .modal-dialog {
                            max-width: 90vw;
                            width: 90vw;
                        }

                        #filePreviewModal .modal-body {
                            padding: 0;
                            max-height: 80vh;
                            overflow-y: auto;
                        }

                        #filePreviewContent {
                            min-height: 400px;
                            position: relative;
                        }

                        .file-preview-loading {
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            z-index: 10;
                        }

                        .file-preview-error {
                            background: #f8f9fa;
                            border: 2px dashed #dee2e6;
                            border-radius: 8px;
                            padding: 2rem;
                            margin: 1rem;
                        }

                        .file-preview-options {
                            background: #f8f9fa;
                            border-radius: 8px;
                            padding: 2rem;
                            margin: 1rem;
                        }

                        .file-preview-options .btn {
                            min-width: 200px;
                            margin: 0.25rem;
                        }

                        .file-size-indicator {
                            font-size: 0.875rem;
                            color: #6c757d;
                            background: #e9ecef;
                            padding: 0.25rem 0.5rem;
                            border-radius: 4px;
                            display: inline-block;
                        }

                        .preview-timeout-warning {
                            background: #fff3cd;
                            border: 1px solid #ffeaa7;
                            color: #856404;
                            padding: 0.75rem;
                            border-radius: 4px;
                            margin: 1rem;
                        }

                        /* Responsive adjustments for file preview */
                        @media (max-width: 768px) {
                            #filePreviewModal .modal-dialog {
                                max-width: 95vw;
                                width: 95vw;
                                margin: 1rem auto;
                            }

                            #filePreviewModal .modal-body {
                                max-height: 70vh;
                            }

                            .file-preview-options .btn {
                                min-width: 100%;
                                margin: 0.25rem 0;
                            }

                            #filePreviewContent iframe {
                                height: 400px !important;
                            }
                        }

                        @media (max-width: 768px) {
                            .card-header .d-flex {
                                flex-direction: column;
                                align-items: flex-start !important;
                                gap: 10px;
                            }

                            .sort-controls {
                                width: 100%;
                                justify-content: space-between;
                            }

                            .sort-controls select {
                                min-width: 150px;
                            }

                            .group-header {
                                font-size: 1rem;
                                padding: 10px 15px;
                                flex-direction: column;
                                align-items: flex-start;
                                gap: 8px;
                            }

                            .group-header .group-info {
                                width: 100%;
                                justify-content: space-between;
                            }

                            .group-header .group-minimize-btn {
                                align-self: flex-end;
                                margin-left: 0;
                                margin-top: -32px;
                            }

                            .card.h-100 {
                                min-height: 180px;
                                max-height: 280px;
                            }

                            .text-tooltip:hover::after {
                                max-width: 250px;
                                font-size: 0.8rem;
                            }

                            .classroom-notifications {
                                padding: 8px;
                            }

                            .notification-item {
                                font-size: 0.8rem !important;
                                padding: 8px !important;
                            }

                            .notifications-list {
                                max-height: 200px;
                            }

                            /* Mobile responsive styles for assessment cards */
                            .assessment-card {
                                min-height: 200px;
                            }

                            .assessment-score {
                                font-size: 1.2rem;
                            }

                            .assessment-percentage {
                                font-size: 1rem;
                            }

                            .assessment-details {
                                font-size: 0.8rem;
                            }

                            .assessment-group-header {
                                font-size: 1rem;
                                padding: 10px 15px;
                                flex-direction: column;
                                align-items: flex-start;
                                gap: 8px;
                            }

                            .assessment-group-header .group-info {
                                width: 100%;
                                justify-content: space-between;
                            }

                            .assessment-group-header .group-minimize-btn {
                                align-self: flex-end;
                                margin-left: 0;
                                margin-top: -32px;
                            }

                            .assessment-group-content .col-xl-4 {
                                margin-bottom: 15px;
                            }
                        }


                    </style>


                    <!-- Content Row Students List Start

                    <div class="row" id="student-assessments-list">
                        <h1 class="h3 mb-0 text-gray-800"></h1>




                    </div>



                    <div class="row" id="g12-students-list">

                        <h1 class="h3 mb-0 text-gray-800">g12 students</h1>




                    </div>

                    Content Row Students List End -->

                    <!-- Content Row Quizzes and Exams List -->
                    <div class="row" id="quizzes-and-exams-list">
                        <div class="col-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <h6 class="m-0 font-weight-bold text-primary">Quizzes and Exams</h6>
                                        <button id="refresh-quizzes-btn" class="btn btn-sm btn-primary">
                                            <i class="fas fa-sync-alt"></i> Refresh
                                        </button>
                                    </div>
                                </div>
                                <div id="quizzes-content">
                                    <div class="card-body">
                                        <div class="row mb-3">
                                            <div class="col-md-6">
                                                <button class="btn btn-primary" id="create-quiz-btn">
                                                    <i class="fas fa-plus"></i> Create Quiz
                                                </button>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="d-flex justify-content-end align-items-center">
                                                    <div class="mr-3">
                                                        <label for="quizzes-group-select" class="form-label mb-0 mr-2">Group by:</label>
                                                        <select class="form-control form-control-sm d-inline-block" id="quizzes-group-select" style="width: auto;">
                                                            <option value="none">No Grouping</option>
                                                            <option value="subject">Subject</option>
                                                            <option value="grade">Grade Level</option>
                                                            <option value="section">Section</option>
                                                            <option value="course">Strand</option>
                                                            <option value="status">Status</option>
                                                        </select>
                                                    </div>
                                                    <div>
                                                        <label for="quizzes-sort-select" class="form-label mb-0 mr-2">Sort by:</label>
                                                        <select class="form-control form-control-sm d-inline-block" id="quizzes-sort-select" style="width: auto;">
                                                            <option value="name-asc">Name (A-Z)</option>
                                                            <option value="name-desc">Name (Z-A)</option>
                                                            <option value="date-newest">Date (Newest)</option>
                                                            <option value="date-oldest">Date (Oldest)</option>
                                                            <option value="deadline-nearest">Deadline (Nearest)</option>
                                                            <option value="deadline-farthest">Deadline (Farthest)</option>
                                                        </select>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="quizzesList">
                                            <!-- Quizzes will be displayed here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Row Quizzes and Exams List End-->



                    <!-- Content Row Students Assessments List -->
                    <div class="row" id="students-assessments-list">
                        <div class="col-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Students Assessments</h6>
                                </div>
                                <div id="assessments-content">
                                    <div class="card-header py-2 bg-light">
                                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                                            <div class="d-flex align-items-center flex-wrap">
                                                <div class="mr-3 mb-2 mb-md-0">
                                                    <label for="assessments-group-select" class="small text-muted mr-2">Group by:</label>
                                                    <select id="assessments-group-select" class="form-control form-control-sm d-inline-block" style="width: auto; min-width: 120px;">
                                                        <option value="none">No Grouping</option>
                                                        <option value="subject">Subject</option>
                                                        <option value="grade">Grade Level</option>
                                                        <option value="section">Section</option>
                                                    </select>
                                                </div>
                                                <div class="mr-3 mb-2 mb-md-0">
                                                    <label for="assessments-sort-select" class="small text-muted mr-2">Sort by:</label>
                                                    <select id="assessments-sort-select" class="form-control form-control-sm d-inline-block" style="width: auto; min-width: 150px;">
                                                        <option value="quiz-name-asc">Quiz Name (A-Z)</option>
                                                        <option value="quiz-name-desc">Quiz Name (Z-A)</option>
                                                        <option value="date-desc">Date Submitted (Newest)</option>
                                                        <option value="date-asc">Date Submitted (Oldest)</option>
                                                        <option value="score-desc">Score (Highest)</option>
                                                        <option value="score-asc">Score (Lowest)</option>
                                                        <option value="student-name-asc">Student Name (A-Z)</option>
                                                        <option value="student-name-desc">Student Name (Z-A)</option>
                                                        <option value="subject-asc">Subject (A-Z)</option>
                                                        <option value="subject-desc">Subject (Z-A)</option>
                                                        <option value="grade-asc">Grade Level (11-12)</option>
                                                        <option value="grade-desc">Grade Level (12-11)</option>
                                                        <option value="section-asc">Section (A-Z)</option>
                                                        <option value="section-desc">Section (Z-A)</option>
                                                    </select>
                                                </div>
                                                <button id="refresh-assessments-btn" class="btn btn-sm btn-primary mb-2 mb-md-0">
                                                    <i class="fas fa-sync-alt"></i> Refresh
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="submissionsList" class="mt-4">
                                            <!-- Quiz submissions will be displayed here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Row Learning Materials List -->
                    <div class="row" id="learning-materials-list">
                        <div class="col-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">My Created Classrooms</h6>
                                </div>
                                <div id="materials-content">
                                    <div class="card-header py-2 bg-light">
                                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                                            <div class="d-flex align-items-center flex-wrap">
                                                <div class="mr-3 mb-2 mb-md-0">
                                                    <label for="materials-group-select" class="small text-muted mr-2">Group by:</label>
                                                    <select id="materials-group-select" class="form-control form-control-sm d-inline-block" style="width: auto; min-width: 120px;">
                                                        <option value="none">No Grouping</option>
                                                        <option value="subject">Subject</option>
                                                        <option value="grade">Grade Level</option>
                                                        <option value="course">Strand</option>
                                                        <option value="section">Section</option>
                                                    </select>
                                                </div>
                                                <div class="mr-3 mb-2 mb-md-0">
                                                    <label for="materials-sort-select" class="small text-muted mr-2">Sort by:</label>
                                                    <select id="materials-sort-select" class="form-control form-control-sm d-inline-block" style="width: auto; min-width: 150px;">
                                                        <option value="name-asc">Name (A-Z)</option>
                                                        <option value="name-desc">Name (Z-A)</option>
                                                        <option value="date-desc">Date Created (Newest)</option>
                                                        <option value="date-asc">Date Created (Oldest)</option>
                                                        <option value="grade-asc">Grade Level (11-12)</option>
                                                        <option value="grade-desc">Grade Level (12-11)</option>
                                                        <option value="course-asc">Strand (A-Z)</option>
                                                        <option value="course-desc">Strand (Z-A)</option>
                                                        <option value="section-asc">Section (A-Z)</option>
                                                        <option value="section-desc">Section (Z-A)</option>
                                                        <option value="materials-desc">Materials (Most)</option>
                                                        <option value="materials-asc">Materials (Least)</option>
                                                    </select>
                                                </div>
                                                <button id="refresh-materials-btn" class="btn btn-sm btn-primary mb-2 mb-md-0">
                                                    <i class="fas fa-sync-alt"></i> Refresh
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <div id="materialsList">
                                            <!-- Classrooms will be displayed here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Content Row Learning Materials List End -->

                    <!-- Content Row Manage Sections -->
                    <div class="row" id="manage-sections-container" style="display: none;">
                        <div class="col-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Manage Sections</h6>
                                </div>
                                <div id="sections-content">
                                    <div class="card-header py-2 bg-light">
                                        <div class="d-flex justify-content-between align-items-center flex-wrap">
                                            <div class="d-flex align-items-center flex-wrap">
                                                <div class="mr-3 mb-2 mb-md-0">
                                                    <label for="sections-group-select" class="small text-muted mr-2">Group by:</label>
                                                    <select id="sections-group-select" class="form-control form-control-sm d-inline-block" style="width: auto; min-width: 120px;">
                                                        <option value="none">No Grouping</option>
                                                        <option value="subject">Subject</option>
                                                        <option value="grade">Grade Level</option>
                                                        <option value="course">Strand</option>
                                                        <option value="section">Section</option>
                                                    </select>
                                                </div>
                                                <div class="mr-3 mb-2 mb-md-0">
                                                    <label for="sections-sort-select" class="small text-muted mr-2">Sort by:</label>
                                                    <select id="sections-sort-select" class="form-control form-control-sm d-inline-block" style="width: auto; min-width: 150px;">
                                                        <option value="name-asc">Name (A-Z)</option>
                                                        <option value="name-desc">Name (Z-A)</option>
                                                        <option value="date-desc">Date Created (Newest)</option>
                                                        <option value="date-asc">Date Created (Oldest)</option>
                                                        <option value="grade-asc">Grade Level (11-12)</option>
                                                        <option value="grade-desc">Grade Level (12-11)</option>
                                                        <option value="course-asc">Strand (A-Z)</option>
                                                        <option value="course-desc">Strand (Z-A)</option>
                                                        <option value="section-asc">Section (A-Z)</option>
                                                        <option value="section-desc">Section (Z-A)</option>
                                                        <option value="students-desc">Students (Most)</option>
                                                        <option value="students-asc">Students (Least)</option>
                                                    </select>
                                                </div>
                                                <button id="refresh-sections-btn" class="btn btn-sm btn-primary mb-2 mb-md-0">
                                                    <i class="fas fa-sync-alt"></i> Refresh
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-body">
                                        <button class="btn btn-primary mb-3" id="teacher-add-classroom-btn">
                                            <i class="fas fa-plus"></i> Add Classroom
                                        </button>
                                        <div id="teacher-classroom-container" class="row">
                                            <!-- Classroom sections will be displayed here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Background Overlay -->
                    <div id="teacher-overlay" class="hidden"></div>

                    <!-- Add Classroom Card -->
                    <div id="teacher-add-classroom-card" class="card shadow hidden">
                        <div class="card-body p-0">
                            <h5 class="text-primary text-uppercase mb-2">Create Classroom</h5>
                            <form id="teacher-add-classroom-form">
                                <div class="form-group mb-2">
                                    <label for="teacher-subject-names" class="small mb-1">Subject Name:</label>
                                    <input type="text" id="teacher-subject-names" name="sname" class="form-control form-control-sm"
                                        placeholder="e.g., Mathematics, English, Science" required>
                                </div>

                                <div class="form-group mb-2">
                                    <label for="teacher-section-name" class="small mb-1">Section Name:</label>
                                    <input type="text" id="teacher-section-name" name="section" class="form-control form-control-sm"
                                        placeholder="e.g., Section A, Einstein, Morning Class" required>
                                </div>

                                <div class="form-group mb-2">
                                    <label for="teacher-detailss" class="small mb-1">Details:</label>
                                    <textarea id="teacher-detailss" name="dss" class="form-control form-control-sm" rows="2"
                                        placeholder="Enter additional details" required></textarea>
                                </div>

                                <div class="row">
                                    <div class="col-6">
                                        <div class="form-group mb-2">
                                            <label for="teacher-grade-levels" class="small mb-1">Grade Level:</label>
                                            <select id="teacher-grade-levels" name="glvl" class="form-control form-control-sm" required>
                                                <option value="Grade 11">Grade 11</option>
                                                <option value="Grade 12">Grade 12</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-group mb-2">
                                            <label for="teacher-courses" class="small mb-1">Strand:</label>
                                            <select id="teacher-courses" name="crs" class="form-control form-control-sm" required>
                                                <option value="STEM">STEM</option>
                                                <option value="HUMSS">HUMSS</option>
                                                <option value="ABM">ABM</option>
                                                <option value="TVL">TVL</option>
                                                <option value="HE">Home Economics</option>
                                                <option value="ICT">ICT</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group mb-3">
                                    <label for="teacher-color-picker" class="small mb-1">Choose a color:</label>
                                    <input type="color" name="clr" id="teacher-color-picker" class="form-control form-control-sm"
                                        value="#4e73df" style="height: 35px;">
                                </div>

                                <div class="d-flex justify-content-between">
                                    <button type="button" class="btn btn-primary btn-sm" id="teacher-submit-classroom">Create</button>
                                    <button type="button" class="btn btn-secondary btn-sm" id="teacher-cancel-classroom">Cancel</button>
                                </div>
                            </form>
                        </div>
                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Content Row Manage Sections End -->

                    <!-- Content Row Students Assessments List -->






                </div>
                <!-- /.container-fluid -->

            </div>
            <!-- End of Main Content -->

            <!-- Footer -->
            <footer class="sticky-footer bg-white">
                <div class="container my-auto">
                    <div class="copyright text-center my-auto">
                        <span>Copyright &copy; LEAD LMS Website 2025</span>
                    </div>
                </div>
            </footer>
            <!-- End of Footer -->

        </div>
        <!-- End of Content Wrapper -->

    </div>
    <!-- End of Page Wrapper -->

    <!-- Scroll to Top Button-->
    <a class="scroll-to-top rounded" href="#page-top">
        <i class="fas fa-angle-up"></i>
    </a>

    <!-- Logout Modal-->
    <div class="modal fade" id="logoutModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel"
        aria-hidden="true">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="exampleModalLabel">Ready to Leave?</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">Select "Logout" below if you are ready to end your current session.</div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Cancel</button>
                    <a class="btn btn-primary" id="logoutButton">Logout</a>
                    <script>
                        document.getElementById('logoutButton').addEventListener('click', function() {
                            // Clear the session timer
                            if (sessionTimer) {
                                clearTimeout(sessionTimer);
                            }
                            // Clear localStorage
                            localStorage.removeItem('teacherData');
                            // Redirect to login page
                            window.location.href = 'index.html';
                        });
                    </script>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div class="modal fade" id="profileModal" tabindex="-1" role="dialog" aria-labelledby="profileModalLabel"
        aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="profileModalLabel">Teacher Profile</h5>
                    <button class="close" type="button" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Full Name</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0" id="modalProfileName"></p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Email</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0" id="modalProfileEmail"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="card shadow mb-4">
                                <div class="card-header py-3">
                                    <h6 class="m-0 font-weight-bold text-primary">Academic Information</h6>
                                </div>
                                <div class="card-body">
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Managed Classes</p>
                                        </div>
                                        <div class="col-sm-9">
                                            <p class="text-muted mb-0" id="modalManagedClasses">0</p>
                                        </div>
                                    </div>
                                    <hr>
                                    <div class="row mb-3">
                                        <div class="col-sm-3">
                                            <p class="mb-0">Teacher ID</p>
                                        </div>
                                        <div class="col-sm-9 d-flex align-items-center">
                                            <p class="text-muted mb-0 mr-3" id="teacherIdDisplay">Not generated</p>
                                            <button class="btn btn-sm btn-primary" id="generateTeacherId">Generate ID</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" type="button" data-dismiss="modal">Return</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Quiz Modal -->
    <div class="modal fade" id="createQuizModal" tabindex="-1" role="dialog" aria-labelledby="createQuizModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="createQuizModalLabel">Create New Quiz</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="quizForm">
                        <div class="form-group">
                            <label for="quizTitle">Quiz Title</label>
                            <input type="text" class="form-control" id="quizTitle" required>
                        </div>

                        <!-- Quiz Settings Section -->
                        <div class="card mb-3">
                            <div class="card-header">
                                <h6 class="m-0 font-weight-bold text-primary">Quiz Settings</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="quizDeadline">Deadline Date & Time</label>
                                            <input type="datetime-local" class="form-control" id="quizDeadline" required>
                                            <small class="form-text text-muted">Students cannot take the quiz after this date and time</small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="quizTimeLimit">Time Limit (minutes)</label>
                                            <input type="number" class="form-control" id="quizTimeLimit" min="1" max="300" value="30" required>
                                            <small class="form-text text-muted">Maximum time allowed to complete the quiz</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="autoSubmit" checked>
                                    <label class="form-check-label" for="autoSubmit">
                                        Auto-submit when time expires
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div id="questionContainer">
                            <!-- Questions will be added here dynamically -->
                        </div>
                        <button type="button" class="btn btn-primary mt-3" id="addQuestionBtn">
                            <i class="fas fa-plus"></i> Add Question
                        </button>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveQuizBtn">Save Quiz</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Send Quiz to Classroom Modal -->
    <div class="modal fade" id="sendQuizModal" tabindex="-1" role="dialog" aria-labelledby="sendQuizModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="sendQuizModalLabel">Send Quiz to Classroom</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <form id="sendQuizForm">
                        <div class="form-group">
                            <label for="selectedQuizTitle">Quiz</label>
                            <input type="text" class="form-control" id="selectedQuizTitle" readonly>
                            <input type="hidden" id="selectedQuizId">
                            <input type="hidden" id="selectedQuizCode">
                        </div>

                        <div class="form-group">
                            <label for="classroomSelect">Select Classroom</label>
                            <select class="form-control" id="classroomSelect" required>
                                <option value="">Loading classrooms...</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="quizMessage">Message (Optional)</label>
                            <textarea class="form-control" id="quizMessage" rows="3" placeholder="Add a message for your students about this quiz..."></textarea>
                        </div>

                        <div class="form-group">
                            <label for="notificationLabel">Notification Label</label>
                            <input type="text" class="form-control" id="notificationLabel" placeholder="e.g., 'New Quiz Available', 'Weekly Assessment'" value="New Quiz Available">
                            <small class="form-text text-muted">This label will appear next to the quiz notification</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="sendQuizBtn">Send Quiz</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap core JavaScript-->
    <script src="vendor/jquery/jquery.min.js"></script>
    <script src="vendor/bootstrap/js/bootstrap.bundle.min.js"></script>

    <!-- Core plugin JavaScript-->
    <script src="vendor/jquery-easing/jquery.easing.min.js"></script>

    <!-- Custom scripts for all pages-->
    <script src="js/sb-admin-2.min.js"></script>

    <!-- File diagnostics for troubleshooting -->
    <script src="js/file-diagnostics.js"></script>

    <!-- Page level plugins
    <script src="vendor/chart.js/Chart.min.js"></script>-->

    <!-- Page level custom scripts
    <script src="js/demo/chart-area-demo.js"></script>
    <script src="js/demo/chart-pie-demo.js"></script>-->

    <!-- Add Firebase SDK - Using v9 compatible with modules -->
    <script type="module">
        // Import Firebase modules
        import { initializeApp } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-app.js";
        import { getFirestore } from "https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js";

        // Firebase configuration
        const firebaseConfig = {
            apiKey: "AIzaSyBuj8sMvbDKmjkAVG5JdVOdEF4OO9ijjzA",
            authDomain: "lead-login.firebaseapp.com",
            projectId: "lead-login",
            storageBucket: "lead-login.appspot.com",
            messagingSenderId: "1051456252675",
            appId: "1:1051456252675:web:61073e11903055f889d736"
        };

        // Initialize Firebase with error handling
        try {
            console.log("Initializing Firebase...");
            const app = initializeApp(firebaseConfig);
            console.log("Firebase initialized successfully");

            const db = getFirestore(app);
            console.log("Firestore initialized successfully");

            // Make db available globally
            window.db = db;
            window.firebaseApp = app;
        } catch (error) {
            console.error("Error initializing Firebase:", error);
            alert("There was an error connecting to the database. Please refresh the page and try again.");
        }
    </script>

    <!-- Teacher Materials Script -->
    <script type="module" src="js/teacher-materials.js"></script>

    <!-- Classroom management scripts -->
    <script type="module" src="js/teacher-classroomcards.js"></script>
    <script type="module" src="js/list-classrooms.js"></script>

    <!-- Teacher Quizzes Script -->
    <script type="module" src="js/teacher-quizzes.js"></script>

    <script>
        // Session timeout duration (30 minutes in milliseconds)
        const SESSION_TIMEOUT = 30 * 60 * 1000;
        let sessionTimer;

        // Function to reset the session timer
        function resetSessionTimer() {
            if (sessionTimer) {
                clearTimeout(sessionTimer);
            }
            sessionTimer = setTimeout(() => {
                console.log('Session timed out due to inactivity');
                handleSessionTimeout();
            }, SESSION_TIMEOUT);
        }

        // Function to handle session timeout
        function handleSessionTimeout() {
            try {
                // Clear localStorage
                localStorage.removeItem('teacherData');
                // Alert the user
                alert('Your session has expired due to inactivity. Please log in again.');
                // Redirect to login page
                window.location.href = 'index.html';
            } catch (error) {
                console.error('Error during session timeout:', error);
            }
        }

        // Add event listeners for user activity
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        activityEvents.forEach(event => {
            document.addEventListener(event, resetSessionTimer);
        });

        // Start the session timer when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            resetSessionTimer();

            // Initialize quizzes display (handled by the more complete event listener below)

            // Event listeners for materials, sections, and assessments are set up below to avoid duplicates

            // Show quizzes list when the button is clicked
            document.getElementById('manage-quizzes-btn').addEventListener('click', async function() {
                document.getElementById('learning-materials-list').style.display = 'none';
                document.getElementById('students-assessments-list').style.display = 'none';
                document.getElementById('manage-sections-container').style.display = 'none';
                document.getElementById('quizzes-and-exams-list').style.display = 'block';

                // Refresh quizzes with enhanced display if available
                if (typeof window.displayQuizzesWithGrouping === 'function') {
                    console.log('Manage quizzes: Using enhanced quiz display');
                    await window.displayQuizzesWithGrouping();
                } else {
                    console.log('Manage quizzes: Enhanced display not available, using basic display');
                    displayQuizzes();
                }
            });

            // Show assessments list when the button is clicked
            document.getElementById('manage-assessments-btn').addEventListener('click', function() {
                document.getElementById('learning-materials-list').style.display = 'none';
                document.getElementById('quizzes-and-exams-list').style.display = 'none';
                document.getElementById('manage-sections-container').style.display = 'none';
                document.getElementById('students-assessments-list').style.display = 'block';

                // Load and display quiz submissions
                displayQuizSubmissions();
            });
        });

        // Update the displayQuizzes function to use the new grouping functionality
        async function displayQuizzes() {
            console.log('displayQuizzes called');
            console.log('displayQuizzesWithGrouping available:', typeof window.displayQuizzesWithGrouping);

            // Use the new grouping function if available, otherwise fallback to basic display
            if (typeof window.displayQuizzesWithGrouping === 'function') {
                console.log('Using enhanced quiz display with grouping');
                await window.displayQuizzesWithGrouping();
            } else {
                console.log('Using fallback quiz display - waiting for module to load...');
                // Wait a bit longer for the module to load
                setTimeout(async () => {
                    if (typeof window.displayQuizzesWithGrouping === 'function') {
                        console.log('Enhanced quiz display now available, switching...');
                        await window.displayQuizzesWithGrouping();
                        return;
                    }
                    console.log('Enhanced quiz display still not available after timeout, using basic display');
                }, 1500);
                // Fallback to basic display (original functionality)
                const quizzesList = document.getElementById('quizzesList');
                const teacherEmail = JSON.parse(localStorage.getItem('teacherData')).email;

                // Show loading state
                quizzesList.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div></div>';

                try {
                    // Import Firestore functions dynamically
                    const { collection, query, where, getDocs } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                    // Make sure we have a Firestore instance
                    if (!window.db) {
                        console.error("Firestore database instance is not available");
                        quizzesList.innerHTML = '<div class="alert alert-danger">Error connecting to database. Please refresh the page.</div>';
                        return;
                    }

                    // Get quizzes from Firestore where createdBy matches the current teacher
                    const quizzesRef = collection(window.db, 'Quizzes');
                    const q = query(quizzesRef, where('createdBy', '==', teacherEmail));
                    const querySnapshot = await getDocs(q);

                    if (querySnapshot.empty) {
                        quizzesList.innerHTML = '<div class="alert alert-info">No quizzes created yet.</div>';
                        return;
                    }

                    const quizzes = [];
                    querySnapshot.forEach((doc) => {
                        quizzes.push({ id: doc.id, ...doc.data() });
                    });

                    // Sort quizzes by createdAt timestamp
                    quizzes.sort((a, b) => {
                        return b.createdAt?.toDate().getTime() - a.createdAt?.toDate().getTime();
                    });

                    quizzesList.innerHTML = quizzes.map((quiz) => `
                        <div class="card shadow mb-4">
                            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                                <h6 class="m-0 font-weight-bold text-dark">${quiz.title}</h6>
                                <div>
                                    <button class="btn btn-info btn-sm mr-2 view-quiz-btn" data-quiz-id="${quiz.id}">
                                        <i class="fas fa-eye"></i> View
                                    </button>
                                    <button class="btn btn-warning btn-sm mr-2 edit-quiz-btn" data-quiz-id="${quiz.id}">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                    <button class="btn btn-success btn-sm mr-2 send-quiz-btn" data-quiz-id="${quiz.id}" data-quiz-title="${quiz.title}" data-quiz-code="${quiz.code}">
                                        <i class="fas fa-paper-plane"></i> Send to Classroom
                                    </button>
                                    <button class="btn btn-danger btn-sm delete-quiz-btn" data-quiz-id="${quiz.id}">
                                        <i class="fas fa-trash"></i> Delete
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <p class="mb-2 text-dark">
                                    <strong>Quiz Code:</strong>
                                    <span class="font-weight-bold text-primary">${quiz.code || 'N/A'}</span>
                                    ${quiz.code ? `<button class="btn btn-sm btn-link ml-2 p-0 copy-quiz-code-btn" data-quiz-code="${quiz.code}" title="Copy quiz code">
                                        <i class="fas fa-copy"></i>
                                    </button>` : ''}
                                </p>
                                <p class="mb-2 text-dark"><strong>Created by:</strong> ${quiz.createdBy}</p>
                                <p class="mb-2 text-dark"><strong>Number of questions:</strong> ${quiz.questions.length}</p>
                                ${quiz.deadline ? `<p class="mb-2 text-dark"><strong>Deadline:</strong> ${new Date(quiz.deadline).toLocaleString()}</p>` : ''}
                                ${quiz.timeLimit ? `<p class="mb-2 text-dark"><strong>Time Limit:</strong> ${quiz.timeLimit} minutes</p>` : ''}
                                ${quiz.autoSubmit !== undefined ? `<p class="mb-2 text-dark"><strong>Auto-submit:</strong> ${quiz.autoSubmit ? 'Enabled' : 'Disabled'}</p>` : ''}
                                ${quiz.deadline && new Date(quiz.deadline) < new Date() ?
                                    '<span class="badge badge-danger">Expired</span>' :
                                    '<span class="badge badge-success">Active</span>'
                                }
                            </div>
                        </div>
                    `).join('');

                    // Add event listeners for view, edit, and delete buttons
                    document.querySelectorAll('.view-quiz-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const quizId = this.getAttribute('data-quiz-id');
                            const quiz = quizzes.find(q => q.id === quizId);
                            viewQuiz(quiz);
                        });
                    });

                    document.querySelectorAll('.edit-quiz-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const quizId = this.getAttribute('data-quiz-id');
                            const quiz = quizzes.find(q => q.id === quizId);
                            editQuiz(quiz);
                        });
                    });

                    document.querySelectorAll('.delete-quiz-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const quizId = this.getAttribute('data-quiz-id');
                            if (confirm('Are you sure you want to delete this quiz?')) {
                                deleteQuiz(quizId);
                            }
                        });
                    });

                    document.querySelectorAll('.send-quiz-btn').forEach(button => {
                        button.addEventListener('click', function() {
                            const quizId = this.getAttribute('data-quiz-id');
                            const quizTitle = this.getAttribute('data-quiz-title');
                            const quizCode = this.getAttribute('data-quiz-code');
                            openSendQuizModal(quizId, quizTitle, quizCode);
                        });
                    });

                    // Add event listeners for copy quiz code buttons
                    document.querySelectorAll('.copy-quiz-code-btn').forEach(button => {
                        button.addEventListener('click', function(e) {
                            e.stopPropagation();
                            const quizCode = this.getAttribute('data-quiz-code');
                            navigator.clipboard.writeText(quizCode).then(() => {
                                const originalIcon = this.innerHTML;
                                this.innerHTML = '<i class="fas fa-check text-success"></i>';
                                this.title = 'Copied!';
                                setTimeout(() => {
                                    this.innerHTML = originalIcon;
                                    this.title = 'Copy quiz code';
                                }, 2000);
                            }).catch(err => {
                                console.error('Failed to copy quiz code: ', err);
                                alert('Failed to copy quiz code. Please try again.');
                            });
                        });
                    });
                } catch (error) {
                    console.error("Error getting quizzes: ", error);
                    quizzesList.innerHTML = '<div class="alert alert-danger">Error loading quizzes. Please try again later.</div>';
                }
            }
        }

        // Function to open send quiz modal
        async function openSendQuizModal(quizId, quizTitle, quizCode) {
            // Set quiz information
            document.getElementById('selectedQuizTitle').value = quizTitle;
            document.getElementById('selectedQuizId').value = quizId;
            document.getElementById('selectedQuizCode').value = quizCode;

            // Load teacher's classrooms
            await loadTeacherClassrooms();

            // Show the modal
            $('#sendQuizModal').modal('show');
        }

        // Function to load teacher's classrooms for the dropdown
        async function loadTeacherClassrooms() {
            try {
                const { collection, query, where, getDocs } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");
                const teacherData = JSON.parse(localStorage.getItem('teacherData'));

                if (!teacherData || !window.db) {
                    console.error("Teacher data or database not available");
                    return;
                }

                // Get classrooms created by this teacher
                const classroomsRef = collection(window.db, 'Classrooms');
                const q = query(classroomsRef, where('createdBy', '==', teacherData.email));
                const querySnapshot = await getDocs(q);

                const classroomSelect = document.getElementById('classroomSelect');

                if (querySnapshot.empty) {
                    classroomSelect.innerHTML = '<option value="">No classrooms found</option>';
                    return;
                }

                let options = '<option value="">Select a classroom</option>';
                querySnapshot.forEach((doc) => {
                    const classroom = doc.data();
                    options += `<option value="${doc.id}">${classroom.subjectName} - ${classroom.gradeLevel} (${classroom.enrollCode})</option>`;
                });

                classroomSelect.innerHTML = options;
            } catch (error) {
                console.error("Error loading classrooms:", error);
                document.getElementById('classroomSelect').innerHTML = '<option value="">Error loading classrooms</option>';
            }
        }

        // Function to send quiz notification to classroom
        async function sendQuizToClassroom() {
            try {
                const { collection, addDoc, updateDoc, doc, query, where, getDocs, getDoc, serverTimestamp } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                const quizId = document.getElementById('selectedQuizId').value;
                const quizTitle = document.getElementById('selectedQuizTitle').value;
                const quizCode = document.getElementById('selectedQuizCode').value;
                const classroomId = document.getElementById('classroomSelect').value;
                const message = document.getElementById('quizMessage').value;
                const label = document.getElementById('notificationLabel').value || 'New Quiz Available';

                if (!classroomId) {
                    alert('Please select a classroom');
                    return;
                }

                const teacherData = JSON.parse(localStorage.getItem('teacherData'));

                // Get classroom details to inherit
                const classroomRef = doc(window.db, 'Classrooms', classroomId);
                const classroomDoc = await getDoc(classroomRef);

                let classroomDetails = {};
                if (classroomDoc.exists()) {
                    const classroom = classroomDoc.data();
                    classroomDetails = {
                        subjectName: classroom.subjectName || '',
                        gradeLevel: classroom.gradeLevel || '',
                        sectionName: classroom.sectionName || '',
                        course: classroom.course || '',
                        enrollCode: classroom.enrollCode || ''
                    };
                    console.log('Inherited classroom details:', classroomDetails);
                } else {
                    console.warn('Classroom not found, using empty details');
                }

                // Check if a notification with the same quiz code and classroom already exists
                const notificationsRef = collection(window.db, 'QuizNotifications');
                const existingNotificationQuery = query(
                    notificationsRef,
                    where("quizCode", "==", quizCode),
                    where("classroomId", "==", classroomId)
                );
                const existingNotificationSnapshot = await getDocs(existingNotificationQuery);

                const notificationData = {
                    type: 'quiz',
                    quizId: quizId,
                    quizTitle: quizTitle,
                    quizCode: quizCode,
                    classroomId: classroomId,
                    message: message,
                    label: label,
                    sentBy: teacherData.email,
                    sentByName: `${teacherData.firstName} ${teacherData.lastName}`,
                    sentAt: serverTimestamp(),
                    isRead: false,
                    // Inherited classroom details for better sorting and grouping
                    ...classroomDetails
                };

                if (!existingNotificationSnapshot.empty) {
                    // Update existing notification instead of creating a new one
                    const existingNotificationDoc = existingNotificationSnapshot.docs[0];
                    const notificationRef = doc(window.db, 'QuizNotifications', existingNotificationDoc.id);

                    await updateDoc(notificationRef, {
                        ...notificationData,
                        updatedAt: serverTimestamp() // Add timestamp to track when it was last updated
                    });

                    alert('Quiz notification updated successfully! The previous notification for this quiz has been replaced.');
                } else {
                    // Create new notification if none exists
                    await addDoc(notificationsRef, notificationData);
                    alert('Quiz sent to classroom successfully!');
                }

                $('#sendQuizModal').modal('hide');

                // Refresh quiz display to show updated classroom information
                if (typeof window.displayQuizzesWithGrouping === 'function') {
                    window.displayQuizzesWithGrouping();
                } else {
                    displayQuizzes();
                }

                // Reset form
                document.getElementById('sendQuizForm').reset();

            } catch (error) {
                console.error("Error sending quiz notification:", error);
                alert('Error sending quiz. Please try again.');
            }
        }

        // Update the viewQuiz function to handle Firestore data
        function viewQuiz(quiz) {
            const modalContent = `
                <div class="modal fade" id="viewQuizModal" tabindex="-1" role="dialog" aria-labelledby="viewQuizModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="viewQuizModalLabel">${quiz.title}</h5>
                                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                            </div>
                            <div class="modal-body">
                                ${quiz.questions.map((q, i) => `
                                    <div class="question-item mb-4 p-3 border rounded">
                                        <h6 class="font-weight-bold">Question ${i + 1}: ${q.text}</h6>
                                        ${q.type === 'multipleChoice' ? `
                                            <div class="choices-container">
                                                ${q.choices.map((choice, j) => `
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" disabled ${j == q.correctAnswer ? 'checked' : ''}>
                                                        <label class="form-check-label">${choice}</label>
                                                    </div>
                                                `).join('')}
                                            </div>
                                        ` : q.type === 'trueFalse' || q.type === 'modifiedTrueFalse' ? `
                                            <p>Correct Answer: ${q.correctAnswer}</p>
                                            ${q.type === 'modifiedTrueFalse' && q.correctAnswer === 'false' ? `
                                                ${q.wrongWord ? `<p>Wrong Word: ${q.wrongWord}</p>` : ''}
                                                <p>Correct Word: ${q.explanation}</p>
                                                <p><span class="badge badge-secondary">Case Insensitive</span></p>
                                            ` : ''}
                                        ` : q.type === 'identification' ? `
                                            <p>Correct Answer: ${q.correctAnswer}
                                                ${q.caseSensitive ?
                                                    '<span class="badge badge-info">Case Sensitive</span>' :
                                                    '<span class="badge badge-secondary">Case Insensitive</span>'}
                                            </p>
                                        ` : `
                                            <p>Correct Answer: ${q.correctAnswer}</p>
                                        `}
                                    </div>
                                `).join('')}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('viewQuizModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add new modal to body
            document.body.insertAdjacentHTML('beforeend', modalContent);

            // Show the modal
            $('#viewQuizModal').modal('show');
        }

        // Update the deleteQuiz function to use Firestore
        async function deleteQuiz(quizId) {
            try {
                // Import Firestore functions dynamically
                const { doc, deleteDoc } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                // Make sure we have a Firestore instance
                if (!window.db) {
                    console.error("Firestore database instance is not available");
                    alert('Error connecting to database. Please refresh the page.');
                    return;
                }

                // Delete the quiz document
                await deleteDoc(doc(window.db, 'Quizzes', quizId));
                console.log("Quiz successfully deleted!");
                // Refresh with enhanced display if available
                if (typeof window.displayQuizzesWithGrouping === 'function') {
                    window.displayQuizzesWithGrouping();
                } else {
                    displayQuizzes();
                }
            } catch (error) {
                console.error("Error removing quiz: ", error);
                alert('Error deleting quiz. Please try again later.');
            }
        }

        // Add edit quiz function
        function editQuiz(quiz) {
            console.log("Editing quiz:", quiz);

            // Set the quiz title
            document.getElementById('quizTitle').value = quiz.title;

            // Set deadline and time limit if they exist
            if (quiz.deadline) {
                // Convert deadline to datetime-local format
                const deadline = new Date(quiz.deadline);
                const localDateTime = new Date(deadline.getTime() - deadline.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
                document.getElementById('quizDeadline').value = localDateTime;
            }
            if (quiz.timeLimit) {
                document.getElementById('quizTimeLimit').value = quiz.timeLimit;
            }
            if (quiz.autoSubmit !== undefined) {
                document.getElementById('autoSubmit').checked = quiz.autoSubmit;
            }

            // Clear existing questions
            document.getElementById('questionContainer').innerHTML = '';

            // Add each question
            quiz.questions.forEach(question => {
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-item mb-4 p-3 border rounded';

                let questionHTML = `
                    <div class="form-group d-flex justify-content-between align-items-center">
                        <label class="mb-0">Question Type</label>
                        <button type="button" class="btn btn-danger btn-sm remove-question">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <select class="form-control question-type mb-3" required>
                        <option value="multipleChoice" ${question.type === 'multipleChoice' ? 'selected' : ''}>Multiple Choice</option>
                        <option value="trueFalse" ${question.type === 'trueFalse' ? 'selected' : ''}>True or False</option>
                        <option value="modifiedTrueFalse" ${question.type === 'modifiedTrueFalse' ? 'selected' : ''}>Modified True or False</option>
                        <option value="identification" ${question.type === 'identification' ? 'selected' : ''}>Identification</option>
                    </select>
                    <div class="form-group">
                        <label>Question</label>
                        <input type="text" class="form-control question-text" value="${question.text}" required>
                    </div>
                    <div class="question-options">
                        <!-- Options will be added here based on question type -->
                    </div>`;

                questionDiv.innerHTML = questionHTML;
                document.getElementById('questionContainer').appendChild(questionDiv);

                // Add event listener for remove button
                const removeButton = questionDiv.querySelector('.remove-question');
                removeButton.addEventListener('click', function() {
                    if (confirm('Are you sure you want to remove this question?')) {
                        questionDiv.remove();
                    }
                });

                // Add event listener for question type change
                const questionTypeSelect = questionDiv.querySelector('.question-type');
                questionTypeSelect.addEventListener('change', function() {
                    updateQuestionOptions(questionDiv, this.value, question);
                });

                // Initialize options for the current question type
                updateQuestionOptions(questionDiv, question.type, question);

                // Add event listener for modified true/false correct answer change
                setTimeout(() => {
                    const correctAnswerSelect = questionDiv.querySelector('.correct-answer');
                    if (correctAnswerSelect) {
                        // Check if this is a modified true/false question
                        const questionType = questionDiv.querySelector('.question-type').value;
                        if (questionType === 'modifiedTrueFalse') {
                            // Trigger the change event to show/hide fields based on initial value
                            handleModifiedTrueFalseChange(correctAnswerSelect);
                        }

                        // Add the event listener (as a backup to the inline onchange)
                        correctAnswerSelect.addEventListener('change', function() {
                            const wrongWordGroup = questionDiv.querySelector('.wrong-word-group');
                            const explanationGroup = questionDiv.querySelector('.explanation-group');
                            if (this.value === 'false') {
                                if (wrongWordGroup) wrongWordGroup.style.display = '';
                                if (explanationGroup) explanationGroup.style.display = '';
                            } else {
                                if (wrongWordGroup) wrongWordGroup.style.display = 'none';
                                if (explanationGroup) explanationGroup.style.display = 'none';
                            }
                        });
                    }
                }, 100);
            });

            // Store the quiz ID and code when editing
            if (quiz && quiz.id) {
                console.log("Setting editing quiz ID:", quiz.id);
                // Add data attributes to the form to indicate we're editing
                const quizForm = document.getElementById('quizForm');
                quizForm.setAttribute('data-editing-quiz-id', quiz.id);

                // Store the quiz code if available
                if (quiz.code) {
                    console.log("Preserving quiz code:", quiz.code);
                    quizForm.setAttribute('data-quiz-code', quiz.code);
                }

                // Also add a hidden input field as a backup
                let hiddenInput = document.getElementById('editingQuizId');
                if (!hiddenInput) {
                    hiddenInput = document.createElement('input');
                    hiddenInput.type = 'hidden';
                    hiddenInput.id = 'editingQuizId';
                    quizForm.appendChild(hiddenInput);
                }
                hiddenInput.value = quiz.id;

                // Update the modal title to indicate we're editing
                document.getElementById('createQuizModalLabel').textContent = 'Edit Quiz';
            }

            // Show the modal
            $('#createQuizModal').modal('show');
        }

        // Make quiz functions globally available
        window.viewQuiz = viewQuiz;
        window.editQuiz = editQuiz;
        window.deleteQuiz = deleteQuiz;
        window.openSendQuizModal = openSendQuizModal;

        function updateQuestionOptions(questionDiv, questionType, existingQuestion = null) {
            const optionsContainer = questionDiv.querySelector('.question-options');
            let optionsHTML = '';

            switch(questionType) {
                case 'multipleChoice':
                    optionsHTML = `
                        <div class="form-group">
                            <label>Choices</label>
                            <div class="choices-container">
                                ${[0, 1, 2, 3].map(i => `
                                    <div class="input-group mb-2">
                                        <div class="input-group-prepend">
                                            <div class="input-group-text">
                                                <input type="radio" name="correctAnswer_${Date.now()}_${Math.random().toString(36).substr(2, 9)}" value="${i}"
                                                    ${existingQuestion && existingQuestion.correctAnswer == i ? 'checked' : ''} required>
                                                <span class="ml-2">Correct</span>
                                            </div>
                                        </div>
                                        <input type="text" class="form-control choice-input"
                                            value="${existingQuestion ? existingQuestion.choices[i] : ''}" required>
                                    </div>
                                `).join('')}
                            </div>
                            <small class="form-text text-muted">Select the correct answer by clicking the radio button next to it.</small>
                        </div>`;
                    break;
                case 'trueFalse':
                    optionsHTML = `
                        <div class="form-group">
                            <label>Correct Answer</label>
                            <select class="form-control correct-answer" required>
                                <option value="true" ${existingQuestion && existingQuestion.correctAnswer === 'true' ? 'selected' : ''}>True</option>
                                <option value="false" ${existingQuestion && existingQuestion.correctAnswer === 'false' ? 'selected' : ''}>False</option>
                            </select>
                        </div>`;
                    break;
                case 'modifiedTrueFalse':
                    // Determine if the wrong word and explanation inputs should be required
                    const isRequiredWrongWord = existingQuestion && existingQuestion.correctAnswer === 'false';
                    const isRequiredExplanation = existingQuestion && existingQuestion.correctAnswer === 'false';

                    optionsHTML = `
                        <div class="form-group">
                            <label>Correct Answer</label>
                            <select class="form-control correct-answer" required onchange="handleModifiedTrueFalseChange(this)">
                                <option value="true" ${existingQuestion && existingQuestion.correctAnswer === 'true' ? 'selected' : ''}>True</option>
                                <option value="false" ${existingQuestion && existingQuestion.correctAnswer === 'false' ? 'selected' : ''}>False</option>
                            </select>
                        </div>
                        <div class="form-group wrong-word-group" ${existingQuestion && existingQuestion.correctAnswer === 'false' ? '' : 'style="display:none;"'}>
                            <label>Wrong Word (if False)</label>
                            <input type="text" class="form-control wrong-word"
                                value="${existingQuestion && existingQuestion.wrongWord ? existingQuestion.wrongWord : ''}"
                                placeholder="Enter the incorrect word in the question"
                                ${isRequiredWrongWord ? 'required' : ''}>
                            <small class="form-text text-muted">Specify which word in the question is incorrect.</small>
                        </div>
                        <div class="form-group explanation-group" ${existingQuestion && existingQuestion.correctAnswer === 'false' ? '' : 'style="display:none;"'}>
                            <label>Correct Replacement Word (if False)</label>
                            <input type="text" class="form-control explanation"
                                value="${existingQuestion ? existingQuestion.explanation || '' : ''}"
                                placeholder="Enter the correct word that should replace the wrong word"
                                ${isRequiredExplanation ? 'required' : ''}>
                            <small class="form-text text-muted">This answer will not be case sensitive.</small>
                        </div>`;
                    break;
                case 'identification':
                    optionsHTML = `
                        <div class="form-group">
                            <label>Correct Answer</label>
                            <input type="text" class="form-control correct-answer"
                                value="${existingQuestion ? existingQuestion.correctAnswer : ''}" required>
                        </div>
                        <div class="form-check mb-3">
                            <input class="form-check-input case-sensitive-check" type="checkbox" id="caseSensitive_${Date.now()}"
                                ${existingQuestion && existingQuestion.caseSensitive ? 'checked' : ''}>
                            <label class="form-check-label" for="caseSensitive_${Date.now()}">
                                Case sensitive answer
                            </label>
                        </div>`;
                    break;
            }

            optionsContainer.innerHTML = optionsHTML;
        }

        // Add function to generate a 6-character code
        function generateQuizCode() {
            const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let code = '';
            for (let i = 0; i < 6; i++) {
                code += characters.charAt(Math.floor(Math.random() * characters.length));
            }
            return code;
        }



        // Function to handle individual group minimize button clicks
        function handleGroupMinimize(e) {
            e.preventDefault();
            e.stopPropagation();

            const button = e.currentTarget;
            const header = button.closest('.group-header, .assessment-group-header');

            if (!header) {
                return;
            }

            const content = header.nextElementSibling;

            if (content && (content.classList.contains('group-content') || content.classList.contains('assessment-group-content'))) {
                // Toggle collapsed state
                const isCollapsed = content.classList.contains('collapsed');

                if (isCollapsed) {
                    // Expand
                    content.classList.remove('collapsed');
                    header.classList.remove('collapsed');
                    button.innerHTML = '<i class="fas fa-chevron-down"></i>';
                } else {
                    // Collapse
                    content.classList.add('collapsed');
                    header.classList.add('collapsed');
                    button.innerHTML = '<i class="fas fa-chevron-right"></i>';
                }

                // Save state to localStorage
                const groupName = header.textContent.trim().split('\n')[0].trim();
                const sectionType = header.classList.contains('assessment-group-header') ? 'assessments' : 'materials';
                const storageKey = `groupCollapsed_${sectionType}_${groupName}`;
                localStorage.setItem(storageKey, !isCollapsed);
            }
        }

        // Function to handle group minimize/expand functionality
        function initializeGroupMinimize() {
            // Add event listeners to all group headers with minimize buttons
            document.addEventListener('click', function(e) {
                // Check if the clicked element is a minimize button or inside one
                const button = e.target.closest('.group-minimize-btn');
                if (button) {
                    handleGroupMinimize(e);
                }
            });
        }

        // Function to restore group collapse states from localStorage
        function restoreGroupStates() {
            // Restore states for both materials and assessments
            document.querySelectorAll('.group-header, .assessment-group-header').forEach(header => {
                const groupName = header.textContent.trim().split('\n')[0].trim();
                const sectionType = header.classList.contains('assessment-group-header') ? 'assessments' : 'materials';
                const storageKey = `groupCollapsed_${sectionType}_${groupName}`;
                const isCollapsed = localStorage.getItem(storageKey) === 'true';

                if (isCollapsed) {
                    const content = header.nextElementSibling;
                    const button = header.querySelector('.group-minimize-btn');

                    if (content && button) {
                        content.classList.add('collapsed');
                        header.classList.add('collapsed');
                        button.innerHTML = '<i class="fas fa-chevron-right"></i>';
                    }
                }
            });
        }

        // Make functions available globally
        window.restoreGroupStates = restoreGroupStates;

        // Update the save quiz button handler to use Firestore
        document.addEventListener('DOMContentLoaded', function () {
            // Initialize group minimize functionality
            initializeGroupMinimize();

            // Hide students assessments list and learning materials list by default
            document.getElementById('students-assessments-list').style.display = 'none';
            document.getElementById('learning-materials-list').style.display = 'none';

            // Get teacher data from localStorage
            const teacherData = JSON.parse(localStorage.getItem('teacherData'));

            if (teacherData) {
                // Update the profile dropdown with teacher information
                document.querySelector('.mr-2.d-none.d-lg-inline.text-gray-600.small').textContent = `${teacherData.firstName} ${teacherData.lastName}`;
                document.getElementById('profileName').textContent = `${teacherData.firstName} ${teacherData.lastName}`;
                document.getElementById('profileEmail').textContent = teacherData.email;

                // Update modal profile information
                document.getElementById('modalProfileName').textContent = `${teacherData.firstName} ${teacherData.lastName}`;
                document.getElementById('modalProfileEmail').textContent = teacherData.email;

                // Display teacher ID if it exists and disable the generate button
                if (teacherData.teacherId) {
                    document.getElementById('teacherIdDisplay').textContent = teacherData.teacherId;
                    // Disable the generate button since ID is already generated
                    const generateButton = document.getElementById('generateTeacherId');
                    if (generateButton) {
                        generateButton.disabled = true;
                        generateButton.textContent = "ID Generated";
                        generateButton.classList.remove('btn-primary');
                        generateButton.classList.add('btn-secondary');
                    }
                }

                // Update welcome message
                document.querySelector('.h3.mb-0.text-gray-800').textContent = `Welcome ${teacherData.firstName}!`;

                // Update managed classes count
                const teacherCards = JSON.parse(localStorage.getItem('teacherCards')) || [];
                document.getElementById('modalManagedClasses').textContent = teacherCards.length;
            } else {
                // If no teacher data is found, redirect to login page
                window.location.href = 'teacherregister.html';
            }

            // Flag to track intentional logout
            let isLoggingOut = false;

            // Add logout functionality
            document.getElementById('logout-btn').addEventListener('click', function (e) {
                e.preventDefault(); // Prevent default link behavior

                // Show confirmation dialog
                if (confirm('Are you sure you want to logout?')) {
                    // Clear the session timer
                    if (sessionTimer) {
                        clearTimeout(sessionTimer);
                    }
                    // Set flag to prevent beforeunload prompt
                    isLoggingOut = true;
                    // Clear localStorage
                    localStorage.removeItem('teacherData');
                    // If confirmed, redirect to login page
                    window.location.href = 'index.html';
                }
            });

            // Add dashboard icon logout confirmation
            document.getElementById('dashboard-icon').addEventListener('click', function(e) {
                e.preventDefault(); // Prevent default link behavior

                // Show confirmation dialog
                if (confirm('Navigating to the homepage will log you out of your current session. Do you want to proceed?')) {
                    // Clear the session timer
                    if (sessionTimer) {
                        clearTimeout(sessionTimer);
                    }
                    // Set flag to prevent beforeunload prompt
                    isLoggingOut = true;
                    // Clear localStorage
                    localStorage.removeItem('teacherData');
                    // If confirmed, redirect to login page
                    window.location.href = 'index.html';
                }
            });





            // Add pagehide event to handle logout when page is actually being closed
            // This is more reliable than unload for cleanup operations
            window.addEventListener('pagehide', function(e) {
                // Clear localStorage when page is actually closing
                localStorage.removeItem('teacherData');

                // Try to sign out if possible (may not complete due to page unloading)
                if (window.auth && typeof window.auth.signOut === 'function') {
                    try {
                        window.auth.signOut().catch(() => {
                            // Ignore errors during page unload
                        });
                    } catch (error) {
                        // Ignore errors during page unload
                    }
                }
            });

            // Add unload event as fallback for older browsers
            window.addEventListener('unload', function(e) {
                // Clear localStorage when page is actually closing
                localStorage.removeItem('teacherData');
            });

            // Add click handler for Manage Quizzes and Exams
            document.getElementById('manage-quizzes-btn').addEventListener('click', function(e) {
                e.preventDefault();
                // Scroll to the quizzes and exams section
                document.getElementById('quizzes-and-exams-list').scrollIntoView({ behavior: 'smooth' });
                // Hide other containers
                document.getElementById('students-assessments-list').style.display = 'none';
                document.getElementById('learning-materials-list').style.display = 'none';
                document.getElementById('manage-sections-container').style.display = 'none';
                // Show the quizzes and exams list
                document.getElementById('quizzes-and-exams-list').style.display = 'block';
            });

            // Add click handler for Manage Students Assessments
            document.getElementById('manage-assessments-btn').addEventListener('click', function(e) {
                e.preventDefault();
                // Scroll to the students assessments section
                document.getElementById('students-assessments-list').scrollIntoView({ behavior: 'smooth' });
                // Show the students assessments list
                document.getElementById('students-assessments-list').style.display = 'block';
                // Hide other containers
                document.getElementById('quizzes-and-exams-list').style.display = 'none';
                document.getElementById('learning-materials-list').style.display = 'none';
                document.getElementById('manage-sections-container').style.display = 'none';
                // Load and display quiz submissions
                displayQuizSubmissions();
            });

            // Add click handler for Manage Learning Materials
            document.getElementById('system-nav').addEventListener('click', function(e) {
                e.preventDefault();
                // Scroll to the learning materials section
                document.getElementById('learning-materials-list').scrollIntoView({ behavior: 'smooth' });
                // Show the learning materials list
                document.getElementById('learning-materials-list').style.display = 'block';
                // Hide other containers
                document.getElementById('quizzes-and-exams-list').style.display = 'none';
                document.getElementById('students-assessments-list').style.display = 'none';
                document.getElementById('manage-sections-container').style.display = 'none';

                // Trigger refresh of materials to ensure they load
                setTimeout(() => {
                    window.dispatchEvent(new CustomEvent('refreshClassrooms'));
                }, 100);
            });

            // Add click handler for Refresh Materials button
            document.getElementById('refresh-materials-btn').addEventListener('click', function(e) {
                e.preventDefault();
                // Show loading spinner
                const refreshBtn = document.getElementById('refresh-materials-btn');
                const originalContent = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
                refreshBtn.disabled = true;

                // Call the displayClassrooms function from teacher-materials.js
                // We need to access it through the window object since it's in a module
                try {
                    // Create and dispatch a custom event that teacher-materials.js will listen for
                    const refreshEvent = new CustomEvent('refreshClassrooms');
                    window.dispatchEvent(refreshEvent);

                    // Reset button after a short delay
                    setTimeout(() => {
                        refreshBtn.innerHTML = originalContent;
                        refreshBtn.disabled = false;
                    }, 1000);
                } catch (error) {
                    console.error('Error refreshing classrooms:', error);
                    refreshBtn.innerHTML = originalContent;
                    refreshBtn.disabled = false;
                    alert('Error refreshing classrooms. Please try again.');
                }
            });

            // Add click handler for Refresh Quizzes button
            document.getElementById('refresh-quizzes-btn').addEventListener('click', async function(e) {
                e.preventDefault();
                // Show loading spinner
                const refreshBtn = document.getElementById('refresh-quizzes-btn');
                const originalContent = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
                refreshBtn.disabled = true;

                try {
                    // Force use of enhanced display if available
                    if (typeof window.displayQuizzesWithGrouping === 'function') {
                        console.log('Refresh: Using enhanced quiz display');
                        await window.displayQuizzesWithGrouping();
                    } else {
                        console.log('Refresh: Enhanced display not available, using basic display');
                        displayQuizzes();
                    }

                    // Reset button after a short delay
                    setTimeout(() => {
                        refreshBtn.innerHTML = originalContent;
                        refreshBtn.disabled = false;
                    }, 1000);
                } catch (error) {
                    console.error('Error refreshing quizzes:', error);
                    refreshBtn.innerHTML = originalContent;
                    refreshBtn.disabled = false;
                    alert('Error refreshing quizzes. Please try again.');
                }
            });

            // Add click handler for Refresh Assessments button
            document.getElementById('refresh-assessments-btn').addEventListener('click', function(e) {
                e.preventDefault();
                // Show loading spinner
                const refreshBtn = document.getElementById('refresh-assessments-btn');
                const originalContent = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
                refreshBtn.disabled = true;

                try {
                    // Call the displayQuizSubmissions function directly since it's in the same file
                    displayQuizSubmissions();

                    // Reset button after a short delay
                    setTimeout(() => {
                        refreshBtn.innerHTML = originalContent;
                        refreshBtn.disabled = false;
                    }, 1000);
                } catch (error) {
                    console.error('Error refreshing assessments:', error);
                    refreshBtn.innerHTML = originalContent;
                    refreshBtn.disabled = false;
                    alert('Error refreshing assessments. Please try again.');
                }
            });

            // Add click handler for Refresh Sections button
            document.getElementById('refresh-sections-btn').addEventListener('click', function(e) {
                e.preventDefault();
                // Show loading spinner
                const refreshBtn = document.getElementById('refresh-sections-btn');
                const originalContent = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
                refreshBtn.disabled = true;

                try {
                    // Create and dispatch a custom event that teacher-classroomcards.js will listen for
                    const refreshEvent = new CustomEvent('refreshSections');
                    window.dispatchEvent(refreshEvent);

                    // Reset button after a short delay
                    setTimeout(() => {
                        refreshBtn.innerHTML = originalContent;
                        refreshBtn.disabled = false;
                    }, 1000);
                } catch (error) {
                    console.error('Error refreshing sections:', error);
                    refreshBtn.innerHTML = originalContent;
                    refreshBtn.disabled = false;
                    alert('Error refreshing sections. Please try again.');
                }
            });

            // Add event listeners for assessment sort and group dropdowns
            const assessmentsSort = document.getElementById('assessments-sort-select');
            const assessmentsGroup = document.getElementById('assessments-group-select');

            if (assessmentsSort) {
                // Load saved sort preference
                const savedSort = localStorage.getItem('assessmentsSortPreference');
                if (savedSort) {
                    assessmentsSort.value = savedSort;
                }

                assessmentsSort.addEventListener('change', function() {
                    console.log('Assessments sort option changed to:', this.value);
                    // Save sort preference
                    localStorage.setItem('assessmentsSortPreference', this.value);
                    displayQuizSubmissions();
                });
            }

            if (assessmentsGroup) {
                // Load saved group preference
                const savedGroup = localStorage.getItem('assessmentsGroupPreference');
                if (savedGroup) {
                    assessmentsGroup.value = savedGroup;
                }

                assessmentsGroup.addEventListener('change', function() {
                    console.log('Assessments group option changed to:', this.value);
                    // Save group preference
                    localStorage.setItem('assessmentsGroupPreference', this.value);
                    displayQuizSubmissions();
                });
            }

            // Create Quiz Button Click Handler
            document.getElementById('create-quiz-btn').addEventListener('click', function() {
                // Reset the form and show the modal
                document.getElementById('quizForm').reset();
                document.getElementById('questionContainer').innerHTML = '';

                // Set default values for new quiz
                const tomorrow = new Date();
                tomorrow.setDate(tomorrow.getDate() + 1);
                tomorrow.setHours(23, 59); // Set to 11:59 PM tomorrow
                const localDateTime = new Date(tomorrow.getTime() - tomorrow.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
                document.getElementById('quizDeadline').value = localDateTime;
                document.getElementById('quizTimeLimit').value = 30; // Default 30 minutes
                document.getElementById('autoSubmit').checked = true; // Default auto-submit enabled

                // Remove any editing quiz ID if present
                document.getElementById('quizForm').removeAttribute('data-editing-quiz-id');

                // Remove hidden input if it exists
                const hiddenInput = document.getElementById('editingQuizId');
                if (hiddenInput) {
                    hiddenInput.remove();
                }

                // Reset the modal title to indicate we're creating a new quiz
                document.getElementById('createQuizModalLabel').textContent = 'Create New Quiz';

                // Show the modal
                $('#createQuizModal').modal('show');
            });

            // Add Question Button Click Handler
            document.getElementById('addQuestionBtn').addEventListener('click', function() {
                const questionContainer = document.getElementById('questionContainer');
                const questionDiv = document.createElement('div');
                questionDiv.className = 'question-item mb-4 p-3 border rounded';

                let questionHTML = `
                    <div class="form-group d-flex justify-content-between align-items-center">
                        <label class="mb-0">Question Type</label>
                        <button type="button" class="btn btn-danger btn-sm remove-question">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    <select class="form-control question-type mb-3" required>
                        <option value="multipleChoice">Multiple Choice</option>
                        <option value="trueFalse">True or False</option>
                        <option value="modifiedTrueFalse">Modified True or False</option>
                        <option value="identification">Identification</option>
                    </select>
                    <div class="form-group">
                        <label>Question</label>
                        <input type="text" class="form-control question-text" required>
                    </div>
                    <div class="question-options">
                        <!-- Options will be added here based on question type -->
                    </div>`;

                questionDiv.innerHTML = questionHTML;
                questionContainer.appendChild(questionDiv);

                // Add event listener for remove button
                const removeButton = questionDiv.querySelector('.remove-question');
                removeButton.addEventListener('click', function() {
                    if (confirm('Are you sure you want to remove this question?')) {
                        questionDiv.remove();
                    }
                });

                // Add event listener for question type change
                const questionTypeSelect = questionDiv.querySelector('.question-type');
                questionTypeSelect.addEventListener('change', function() {
                    updateQuestionOptions(questionDiv, this.value);
                });

                // Initialize options for the default question type
                updateQuestionOptions(questionDiv, questionTypeSelect.value);

                // Add event listener for modified true/false correct answer change
                setTimeout(() => {
                    const correctAnswerSelect = questionDiv.querySelector('.correct-answer');
                    if (correctAnswerSelect) {
                        // Check if this is a modified true/false question
                        const questionType = questionDiv.querySelector('.question-type').value;
                        if (questionType === 'modifiedTrueFalse') {
                            // Trigger the change event to show/hide fields based on initial value
                            handleModifiedTrueFalseChange(correctAnswerSelect);
                        }

                        // Add the event listener (as a backup to the inline onchange)
                        correctAnswerSelect.addEventListener('change', function() {
                            const wrongWordGroup = questionDiv.querySelector('.wrong-word-group');
                            const explanationGroup = questionDiv.querySelector('.explanation-group');
                            if (this.value === 'false') {
                                if (wrongWordGroup) wrongWordGroup.style.display = '';
                                if (explanationGroup) explanationGroup.style.display = '';
                            } else {
                                if (wrongWordGroup) wrongWordGroup.style.display = 'none';
                                if (explanationGroup) explanationGroup.style.display = 'none';
                            }
                        });
                    }
                }, 100);
            });

            // Save Quiz Button Click Handler
            document.getElementById('saveQuizBtn').addEventListener('click', async function(e) {
                e.preventDefault();
                console.log("Save Quiz button clicked");

                try {
                    // Check if Firebase is initialized (using window.db which is set in the Firebase initialization)
                    if (!window.db) {
                        console.error("Firebase is not initialized properly");
                        alert('Error: Firebase is not initialized. Please refresh the page and try again.');
                        return;
                    }

                    const quizTitle = document.getElementById('quizTitle').value;
                    if (!quizTitle) {
                        alert('Please enter a quiz title');
                        return;
                    }

                    const questions = [];
                    const questionItems = document.querySelectorAll('.question-item');

                    if (questionItems.length === 0) {
                        alert('Please add at least one question');
                        return;
                    }

                    console.log("Form validation passed, processing questions...");

                let hasError = false;
                try {
                    // Process each question one by one
                    for (let i = 0; i < questionItems.length; i++) {
                        const item = questionItems[i];
                        const index = i;
                        console.log(`Processing question ${index + 1}`);

                        try {
                            const questionType = item.querySelector('.question-type').value;
                            const questionText = item.querySelector('.question-text').value;

                            if (!questionText) {
                                alert(`Please enter text for question ${index + 1}`);
                                hasError = true;
                                break;
                            }

                            const question = {
                                text: questionText,
                                type: questionType
                            };

                            if (questionType === 'multipleChoice') {
                                const choices = Array.from(item.querySelectorAll('.choice-input')).map(input => input.value);
                                if (choices.some(choice => !choice)) {
                                    alert(`Please fill in all choices for multiple choice question ${index + 1}`);
                                    hasError = true;
                                    break;
                                }
                                question.choices = choices;
                                const correctAnswerRadio = item.querySelector('input[type="radio"]:checked');
                                if (!correctAnswerRadio) {
                                    alert(`Please select the correct answer for multiple choice question ${index + 1}`);
                                    hasError = true;
                                    break;
                                }
                                question.correctAnswer = parseInt(correctAnswerRadio.value);
                                console.log(`Multiple choice question processed: ${questionText}`);
                            } else if (questionType === 'trueFalse' || questionType === 'modifiedTrueFalse') {
                                const correctAnswerSelect = item.querySelector('.correct-answer');
                                if (!correctAnswerSelect || !correctAnswerSelect.value) {
                                    alert(`Please select the correct answer for true/false question ${index + 1}`);
                                    hasError = true;
                                    break;
                                }
                                question.correctAnswer = correctAnswerSelect.value;

                                if (questionType === 'modifiedTrueFalse' && correctAnswerSelect.value === 'false') {
                                    // Get the wrong word input element
                                    const wrongWordInput = item.querySelector('.wrong-word');
                                    if (!wrongWordInput) {
                                        console.error(`Wrong word input not found for question ${index + 1}`);
                                        alert(`Error: Wrong word input field not found for question ${index + 1}. Please try again.`);
                                        hasError = true;
                                        break;
                                    }

                                    // Get the explanation input element
                                    const explanationInput = item.querySelector('.explanation');
                                    if (!explanationInput) {
                                        console.error(`Explanation input not found for question ${index + 1}`);
                                        alert(`Error: Correct word input field not found for question ${index + 1}. Please try again.`);
                                        hasError = true;
                                        break;
                                    }

                                    const wrongWord = wrongWordInput.value;
                                    const explanation = explanationInput.value;

                                    if (!wrongWord) {
                                        alert(`Please specify which word in question ${index + 1} is incorrect`);
                                        hasError = true;
                                        break;
                                    }

                                    if (!explanation) {
                                        alert(`Please provide the correct replacement word for question ${index + 1}`);
                                        hasError = true;
                                        break;
                                    }

                                    question.wrongWord = wrongWord;
                                    question.explanation = explanation;
                                }
                                console.log(`True/False question processed: ${questionText}`);
                            } else if (questionType === 'identification') {
                                const correctAnswer = item.querySelector('.correct-answer').value;
                                if (!correctAnswer) {
                                    alert(`Please enter the correct answer for identification question ${index + 1}`);
                                    hasError = true;
                                    break;
                                }
                                question.correctAnswer = correctAnswer;
                                // Add case sensitivity option
                                const caseSensitiveCheck = item.querySelector('.case-sensitive-check');
                                question.caseSensitive = caseSensitiveCheck ? caseSensitiveCheck.checked : false;
                                console.log(`Identification question processed: ${questionText}`);
                            }

                            questions.push(question);
                        } catch (questionError) {
                            console.error(`Error processing question ${index + 1}:`, questionError);
                            alert(`Error processing question ${index + 1}: ${questionError.message || 'Unknown error'}`);
                            hasError = true;
                            break;
                        }
                    }
                } catch (error) {
                    console.error("Error processing questions:", error);
                    alert("An error occurred while processing the questions: " + (error.message || "Unknown error"));
                    hasError = true;
                }

                if (hasError) return;

                // Check if we're editing an existing quiz
                const quizForm = document.getElementById('quizForm');
                let isEditing = quizForm.hasAttribute('data-editing-quiz-id');
                let editingQuizId = isEditing ? quizForm.getAttribute('data-editing-quiz-id') : null;

                // Backup check for hidden input field
                const hiddenInput = document.getElementById('editingQuizId');
                if (!editingQuizId && hiddenInput && hiddenInput.value) {
                    isEditing = true;
                    editingQuizId = hiddenInput.value;
                    console.log("Using backup quiz ID from hidden input:", editingQuizId);
                }

                // Import serverTimestamp dynamically
                const { serverTimestamp } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                // Get deadline and time limit values
                const quizDeadline = document.getElementById('quizDeadline').value;
                const quizTimeLimit = parseInt(document.getElementById('quizTimeLimit').value);
                const autoSubmit = document.getElementById('autoSubmit').checked;

                // Validate deadline is in the future
                if (new Date(quizDeadline) <= new Date()) {
                    alert('Deadline must be in the future');
                    return;
                }

                const quizData = {
                    title: quizTitle,
                    questions: questions,
                    deadline: quizDeadline,
                    timeLimit: quizTimeLimit,
                    autoSubmit: autoSubmit,
                    createdAt: serverTimestamp(),
                    createdBy: JSON.parse(localStorage.getItem('teacherData')).email
                };

                if (!isEditing) {
                    // Only generate code for new quizzes
                    quizData.code = generateQuizCode();
                } else {
                    // For editing, preserve the existing code if available
                    const existingCode = document.getElementById('quizForm').getAttribute('data-quiz-code');
                    if (existingCode) {
                        quizData.code = existingCode;
                    }
                }

                console.log("Saving quiz data:", quizData);
                console.log("Is editing:", isEditing, "Quiz ID:", editingQuizId);

                // Import Firestore functions dynamically
                const { doc, updateDoc, collection, addDoc } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                // Save to Firestore
                if (isEditing && editingQuizId) {
                    console.log("Updating existing quiz with ID:", editingQuizId);
                    // Update existing quiz
                    try {
                        const quizDocRef = doc(window.db, 'Quizzes', editingQuizId);
                        await updateDoc(quizDocRef, quizData);

                        console.log("Quiz updated successfully");
                        // Reset the form
                        document.getElementById('quizForm').reset();
                        document.getElementById('questionContainer').innerHTML = '';

                        // Remove the editing quiz ID and code
                        const quizForm = document.getElementById('quizForm');
                        quizForm.removeAttribute('data-editing-quiz-id');
                        quizForm.removeAttribute('data-quiz-code');

                        // Remove hidden input if it exists
                        const hiddenInput = document.getElementById('editingQuizId');
                        if (hiddenInput) {
                            hiddenInput.remove();
                        }

                        // Reset the modal title
                        document.getElementById('createQuizModalLabel').textContent = 'Create New Quiz';

                        // Hide the modal
                        $('#createQuizModal').modal('hide');

                        // Refresh the quizzes list with enhanced display if available
                        if (typeof window.displayQuizzesWithGrouping === 'function') {
                            window.displayQuizzesWithGrouping();
                        } else {
                            displayQuizzes();
                        }

                        // Show success message
                        alert('Quiz updated successfully!');
                    } catch (error) {
                        console.error("Error updating quiz: ", error);
                        alert('Error updating quiz. Please try again later.');
                    }
                } else {
                    // Create new quiz
                    console.log("Creating new quiz with data:", quizData);

                    // Ensure we have a valid Firestore reference
                    if (!window.db) {
                        console.error("Firestore db object is invalid:", window.db);
                        alert('Database connection error. Please refresh the page and try again.');
                        return;
                    }

                    try {
                        const quizzesCollectionRef = collection(window.db, 'Quizzes');
                        const docRef = await addDoc(quizzesCollectionRef, quizData);

                        console.log("Quiz created successfully with ID: ", docRef.id);
                        alert(`Quiz created successfully! Your quiz code is: ${quizData.code}`);
                        // Reset the form
                        document.getElementById('quizForm').reset();
                        document.getElementById('questionContainer').innerHTML = '';

                        // Remove any editing quiz ID and code if present
                        const quizForm = document.getElementById('quizForm');
                        quizForm.removeAttribute('data-editing-quiz-id');
                        quizForm.removeAttribute('data-quiz-code');

                        // Remove hidden input if it exists
                        const hiddenInput = document.getElementById('editingQuizId');
                        if (hiddenInput) {
                            hiddenInput.remove();
                        }

                        // Reset the modal title
                        document.getElementById('createQuizModalLabel').textContent = 'Create New Quiz';

                        // Hide the modal
                        $('#createQuizModal').modal('hide');

                        // Refresh the quizzes list with enhanced display if available
                        if (typeof window.displayQuizzesWithGrouping === 'function') {
                            window.displayQuizzesWithGrouping();
                        } else {
                            displayQuizzes();
                        }
                    } catch (error) {
                        console.error("Error creating quiz: ", error);
                        alert('Error creating quiz. Please try again later.');
                    }
                }
                } catch (error) {
                    console.error("Error in quiz save process:", error);
                    alert('An error occurred while saving the quiz. Please check the console for details and try again.');
                }
            });

            // Send Quiz Button Click Handler
            document.getElementById('sendQuizBtn').addEventListener('click', function(e) {
                e.preventDefault();
                sendQuizToClassroom();
            });

            // Upload material functionality is now handled by teacher-materials.js

            // Add event listener for the Generate Teacher ID button
            document.getElementById('generateTeacherId').addEventListener('click', async function() {
                try {
                    // Confirm with the user that this is a permanent action
                    const confirmGenerate = confirm('This will generate a permanent 4-digit ID for your account. This action cannot be undone and the ID cannot be changed later. Do you want to continue?');

                    if (!confirmGenerate) {
                        return; // User cancelled the action
                    }

                    // Get the current user ID from localStorage
                    const userId = localStorage.getItem('loggedInUserId');
                    if (!userId) {
                        alert('User ID not found. Please log in again.');
                        return;
                    }

                    // Generate a random 4-digit code
                    const teacherId = generateTeacherId();

                    // Update the display
                    document.getElementById('teacherIdDisplay').textContent = teacherId;

                    // Disable the button to prevent generating multiple IDs
                    this.disabled = true;
                    this.textContent = "ID Generated";
                    this.classList.remove('btn-primary');
                    this.classList.add('btn-secondary');

                    // Import Firestore functions dynamically
                    const { doc, updateDoc } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                    // Update Firestore
                    const teacherRef = doc(window.db, 'Instructors', userId);
                    await updateDoc(teacherRef, {
                        teacherId: teacherId
                    });

                    // Update localStorage
                    const teacherData = JSON.parse(localStorage.getItem('teacherData'));
                    teacherData.teacherId = teacherId;
                    localStorage.setItem('teacherData', JSON.stringify(teacherData));

                    alert('Teacher ID generated successfully! Your permanent ID is: ' + teacherId + '. Please make note of this ID as it will be used for identification purposes.');
                } catch (error) {
                    console.error('Error generating teacher ID:', error);
                    alert('Error generating teacher ID. Please try again.');
                }
            });

            // Function to generate a random 4-digit code
            function generateTeacherId() {
                return Math.floor(1000 + Math.random() * 9000).toString();
            }

            displayQuizzes();
        });

        // Add function to display quiz submissions
        async function displayQuizSubmissions() {
            try {
                // Import Firestore functions dynamically
                const { collection, query, where, getDocs } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                const submissionsList = document.getElementById('submissionsList');
                const teacherEmail = JSON.parse(localStorage.getItem('teacherData')).email;

                // Show loading state
                submissionsList.innerHTML = '<div class="text-center"><div class="spinner-border text-primary" role="status"></div></div>';

                // Make sure we have a Firestore instance
                if (!window.db) {
                    console.error("Firestore database instance is not available");
                    submissionsList.innerHTML = '<div class="alert alert-danger">Error connecting to database. Please refresh the page.</div>';
                    return;
                }

                // First get all quizzes created by this teacher
                const quizzesRef = collection(window.db, 'Quizzes');
                const quizzesQuery = query(quizzesRef, where('createdBy', '==', teacherEmail));
                const quizSnapshot = await getDocs(quizzesQuery);

                if (quizSnapshot.empty) {
                    submissionsList.innerHTML = `
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> No quizzes created yet</h5>
                            <p>You haven't created any quizzes yet. To see student assessments:</p>
                            <ol class="mb-0">
                                <li>Go to the "Quizzes and Exams" section</li>
                                <li>Create a new quiz</li>
                                <li>Send the quiz to your classrooms</li>
                                <li>Student submissions will appear here once they take the quiz</li>
                            </ol>
                        </div>
                    `;
                    return;
                }

                // Create a map of quiz data for easy lookup
                const quizData = {};
                quizSnapshot.docs.forEach(doc => {
                    quizData[doc.id] = { id: doc.id, ...doc.data() };
                });

                const quizIds = quizSnapshot.docs.map(doc => doc.id);

                // Then get all submissions for these quizzes
                const submissionsRef = collection(window.db, 'QuizSubmissions');
                const submissionsQuery = query(submissionsRef, where('quizId', 'in', quizIds));
                const submissionSnapshot = await getDocs(submissionsQuery);

                if (submissionSnapshot.empty) {
                    submissionsList.innerHTML = `
                        <div class="alert alert-info">
                            <h5><i class="fas fa-info-circle"></i> No submissions found yet</h5>
                            <p>Students haven't submitted any quiz responses yet. Once students start taking quizzes, their submissions will appear here with the following features:</p>
                            <ul class="mb-0">
                                <li>Sort by quiz name, submission date, score, student name, subject, grade, or section</li>
                                <li>Group submissions by subject, grade level, or section</li>
                                <li>View detailed answers for each submission</li>
                                <li>Color-coded score indicators (Green: 90%+, Blue: 80-89%, Yellow: 70-79%, Red: <70%)</li>
                            </ul>
                        </div>
                    `;
                    return;
                }

                // Get quiz notifications to find classroom information
                const notificationsRef = collection(window.db, 'QuizNotifications');
                const notificationsQuery = query(notificationsRef, where('sentBy', '==', teacherEmail));
                const notificationsSnapshot = await getDocs(notificationsQuery);

                // Create a map of quiz ID to classroom ID
                const quizToClassroomMap = {};
                notificationsSnapshot.forEach(doc => {
                    const notification = doc.data();
                    if (notification.quizId && notification.classroomId) {
                        quizToClassroomMap[notification.quizId] = notification.classroomId;
                    }
                });

                // Get all classroom data for the teacher
                const classroomsRef = collection(window.db, 'Classrooms');
                const classroomsQuery = query(classroomsRef, where('createdBy', '==', teacherEmail));
                const classroomsSnapshot = await getDocs(classroomsQuery);

                // Create a map of classroom ID to classroom data
                const classroomData = {};
                classroomsSnapshot.forEach(doc => {
                    classroomData[doc.id] = { id: doc.id, ...doc.data() };
                });

                console.log('Quiz to classroom mapping:', quizToClassroomMap);
                console.log('Classroom data:', classroomData);
                console.log('Total submissions found:', submissionSnapshot.size);

                // Also try to get classroom information from student enrollments
                const enrollmentsRef = collection(window.db, 'Enrollments');
                const enrollmentsSnapshot = await getDocs(enrollmentsRef);

                // Create a map of student email to their enrolled classrooms
                const studentToClassroomMap = {};
                enrollmentsSnapshot.forEach(doc => {
                    const enrollment = doc.data();
                    if (enrollment.studentId && enrollment.classroomId) {
                        if (!studentToClassroomMap[enrollment.studentId]) {
                            studentToClassroomMap[enrollment.studentId] = [];
                        }
                        studentToClassroomMap[enrollment.studentId].push(enrollment.classroomId);
                    }
                });

                console.log('Student to classroom mapping:', studentToClassroomMap);

                // Collect all submissions with quiz and classroom data
                const allSubmissions = [];
                submissionSnapshot.forEach(doc => {
                    const submission = doc.data();
                    submission.docId = doc.id;
                    const quiz = quizData[submission.quizId];
                    if (quiz) {
                        submission.quizTitle = quiz.title;

                        // Get classroom information from the quiz-classroom mapping
                        const classroomId = quizToClassroomMap[submission.quizId];
                        const classroom = classroomData[classroomId];

                        if (classroom) {
                            submission.quizSubject = classroom.subjectName || 'Unknown Subject';
                            submission.quizGrade = classroom.gradeLevel || 'Unknown Grade';
                            submission.quizSection = classroom.sectionName || 'Unknown Section';
                            submission.quizEnrollCode = classroom.enrollCode || '';
                        } else {
                            // If no classroom found through notifications, try to find it through student enrollment
                            let foundClassroom = null;

                            // Check if this student is enrolled in any of the teacher's classrooms
                            const studentClassrooms = studentToClassroomMap[submission.studentId] || [];
                            for (const studentClassroomId of studentClassrooms) {
                                if (classroomData[studentClassroomId]) {
                                    foundClassroom = classroomData[studentClassroomId];
                                    break;
                                }
                            }

                            // If still no classroom found, use the first available classroom from the teacher
                            if (!foundClassroom && Object.keys(classroomData).length > 0) {
                                foundClassroom = Object.values(classroomData)[0];
                            }

                            if (foundClassroom) {
                                submission.quizSubject = foundClassroom.subjectName || 'Unknown Subject';
                                submission.quizGrade = foundClassroom.gradeLevel || 'Unknown Grade';
                                submission.quizSection = foundClassroom.sectionName || 'Unknown Section';
                                submission.quizEnrollCode = foundClassroom.enrollCode || '';
                            } else {
                                // Final fallback values
                                submission.quizSubject = quiz.subject || submission.quizTitle || 'General Quiz';
                                submission.quizGrade = quiz.gradeLevel || 'All Grades';
                                submission.quizSection = quiz.section || 'General';
                                submission.quizEnrollCode = quiz.enrollCode || '';
                            }

                            // Log for debugging
                            console.log('No direct classroom mapping found for quiz:', submission.quizId, 'Used enrollment-based approach');
                        }

                        allSubmissions.push(submission);
                    }
                });

                // Get grouping and sorting options
                const groupOption = document.getElementById('assessments-group-select')?.value ||
                                   localStorage.getItem('assessmentsGroupPreference') || 'none';
                const sortOption = document.getElementById('assessments-sort-select')?.value ||
                                  localStorage.getItem('assessmentsSortPreference') || 'quiz-name-asc';

                // Sort submissions
                allSubmissions.sort((a, b) => sortAssessments(a, b, sortOption));

                // Group submissions
                const groupedSubmissions = groupAssessments(allSubmissions, groupOption);

                // Create HTML for grouped submissions
                let html = '';
                Object.keys(groupedSubmissions).forEach(groupName => {
                    const submissionsInGroup = groupedSubmissions[groupName];

                    // Add group header if grouping is enabled
                    if (groupOption !== 'none') {
                        html += `
                            <div class="assessment-group-header">
                                <div class="group-info">
                                    ${groupName}
                                    <span class="group-count">${submissionsInGroup.length} submission${submissionsInGroup.length !== 1 ? 's' : ''}</span>
                                </div>
                                <button class="group-minimize-btn" type="button">
                                    <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                        `;
                    }

                    html += '<div class="assessment-group-content"><div class="row">';

                    // Create cards for submissions in this group
                    submissionsInGroup.forEach(submission => {
                        const score = submission.score || 0;
                        const maxScore = submission.maxScore || 0;
                        const percentage = submission.percentage ? Number(submission.percentage).toFixed(1) : 0;
                        const scoreClass = getScoreClass(percentage);

                        html += `
                            <div class="col-xl-4 col-md-6 mb-4">
                                <div class="card assessment-card shadow h-100 py-2">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                                    ${submission.quizTitle || 'Unknown Quiz'}
                                                </div>
                                                <div class="h5 mb-0 font-weight-bold text-gray-800">
                                                    ${submission.studentName || 'N/A'}
                                                </div>
                                                <div class="assessment-details mt-2">
                                                    ${submission.quizSubject !== 'Unknown Subject' ? `<div><strong>Subject:</strong> ${submission.quizSubject}</div>` : ''}
                                                    ${submission.quizGrade !== 'Unknown Grade' ? `<div><strong>Grade:</strong> ${submission.quizGrade}</div>` : ''}
                                                    ${submission.quizSection !== 'Unknown Section' ? `<div><strong>Section:</strong> ${submission.quizSection}</div>` : ''}
                                                    <div><strong>Submitted:</strong> ${formatSubmissionDate(submission.submittedAt)}</div>
                                                    ${submission.quizEnrollCode ? `<div><strong>Code:</strong> ${submission.quizEnrollCode}</div>` : ''}
                                                </div>
                                            </div>
                                            <div class="col-auto">
                                                <div class="text-center">
                                                    <div class="assessment-score ${scoreClass}">
                                                        ${score}/${maxScore}
                                                    </div>
                                                    <div class="assessment-percentage ${scoreClass}">
                                                        ${percentage}%
                                                    </div>
                                                    <button class="btn btn-sm btn-primary mt-2 view-answers-btn"
                                                        data-quiz-id="${submission.quizId}"
                                                        data-submission-id="${submission.docId || ''}"
                                                        data-student-name="${submission.studentName ? submission.studentName.replace(/'/g, '&#39;') : 'N/A'}"
                                                        data-quiz-title="${submission.quizTitle ? submission.quizTitle.replace(/'/g, '&#39;') : ''}"
                                                        onclick="viewStudentAnswers(this.getAttribute('data-quiz-id'), this.getAttribute('data-submission-id'), this.getAttribute('data-student-name'), this.getAttribute('data-quiz-title'))">
                                                        <i class="fas fa-eye"></i> View
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;
                    });

                    html += '</div></div>';
                });

                submissionsList.innerHTML = html || '<div class="alert alert-info">No submissions found yet.</div>';

                // Add event listeners to newly created minimize buttons
                setTimeout(() => {
                    document.querySelectorAll('.group-minimize-btn').forEach(button => {
                        // Remove any existing listeners to avoid duplicates
                        button.removeEventListener('click', handleGroupMinimize);
                        // Add the event listener
                        button.addEventListener('click', handleGroupMinimize);
                    });

                    // Restore group collapse states after content is loaded
                    restoreGroupStates();
                }, 100);
            } catch (error) {
                console.error("Error getting quiz submissions: ", error);
                submissionsList.innerHTML = '<div class="alert alert-danger">Error loading submissions. Please try again later.</div>';
            }
        }

        // Function to group assessments based on the selected option
        function groupAssessments(submissions, groupOption) {
            if (groupOption === 'none') {
                return { 'All Submissions': submissions };
            }

            const groups = {};

            submissions.forEach(submission => {
                let groupKey;

                switch (groupOption) {
                    case 'subject':
                        groupKey = submission.quizSubject || 'Unknown Subject';
                        break;
                    case 'grade':
                        groupKey = submission.quizGrade || 'Unknown Grade';
                        break;
                    case 'section':
                        groupKey = submission.quizSection || 'Unknown Section';
                        break;
                    default:
                        groupKey = 'All Submissions';
                }

                if (!groups[groupKey]) {
                    groups[groupKey] = [];
                }
                groups[groupKey].push(submission);
            });

            return groups;
        }

        // Function to sort assessments based on the selected option
        function sortAssessments(a, b, sortOption) {
            switch (sortOption) {
                case 'quiz-name-asc':
                    return (a.quizTitle || '').localeCompare(b.quizTitle || '');
                case 'quiz-name-desc':
                    return (b.quizTitle || '').localeCompare(a.quizTitle || '');
                case 'date-desc':
                    const dateA = a.submittedAt?.toDate?.() || new Date(a.submittedAt || 0);
                    const dateB = b.submittedAt?.toDate?.() || new Date(b.submittedAt || 0);
                    return dateB.getTime() - dateA.getTime();
                case 'date-asc':
                    const dateA2 = a.submittedAt?.toDate?.() || new Date(a.submittedAt || 0);
                    const dateB2 = b.submittedAt?.toDate?.() || new Date(b.submittedAt || 0);
                    return dateA2.getTime() - dateB2.getTime();
                case 'score-desc':
                    const percentageA = Number(a.percentage || 0);
                    const percentageB = Number(b.percentage || 0);
                    return percentageB - percentageA;
                case 'score-asc':
                    const percentageA2 = Number(a.percentage || 0);
                    const percentageB2 = Number(b.percentage || 0);
                    return percentageA2 - percentageB2;
                case 'student-name-asc':
                    return (a.studentName || '').localeCompare(b.studentName || '');
                case 'student-name-desc':
                    return (b.studentName || '').localeCompare(a.studentName || '');
                case 'subject-asc':
                    return (a.quizSubject || '').localeCompare(b.quizSubject || '');
                case 'subject-desc':
                    return (b.quizSubject || '').localeCompare(a.quizSubject || '');
                case 'grade-asc':
                    return (a.quizGrade || '').localeCompare(b.quizGrade || '');
                case 'grade-desc':
                    return (b.quizGrade || '').localeCompare(a.quizGrade || '');
                case 'section-asc':
                    return (a.quizSection || '').localeCompare(b.quizSection || '');
                case 'section-desc':
                    return (b.quizSection || '').localeCompare(a.quizSection || '');
                default:
                    return 0;
            }
        }

        // Function to get score class based on percentage
        function getScoreClass(percentage) {
            const score = Number(percentage);
            if (score >= 90) return 'score-excellent';
            if (score >= 80) return 'score-good';
            if (score >= 70) return 'score-average';
            return 'score-poor';
        }

        // Add helper function to format submission date
        function formatSubmissionDate(date) {
            if (!date) return 'N/A';

            try {
                // If it's a Firestore Timestamp
                if (date.toDate) {
                    return date.toDate().toLocaleString();
                }
                // If it's a regular Date object
                else if (date instanceof Date) {
                    return date.toLocaleString();
                }
                // If it's a timestamp number
                else if (typeof date === 'number') {
                    return new Date(date).toLocaleString();
                }
                // If it's a date string
                else if (typeof date === 'string') {
                    return new Date(date).toLocaleString();
                }
                // If none of the above, return N/A
                return 'N/A';
            } catch (error) {
                console.error('Error formatting date:', error);
                return 'N/A';
            }
        }

        // Learning materials functionality is now handled by teacher-materials.js

        // Function to handle viewing student answers is now implemented as viewStudentAnswers
        // and called directly from the button's onclick event

        // Function to handle modified true/false answer change
        // Make this function available globally
        window.handleModifiedTrueFalseChange = function(selectElement) {
            try {
                console.log("handleModifiedTrueFalseChange called with value:", selectElement.value);
                const questionDiv = selectElement.closest('.question-item');
                if (!questionDiv) {
                    console.error("Could not find parent question-item element");
                    return;
                }

                const wrongWordGroup = questionDiv.querySelector('.wrong-word-group');
                const explanationGroup = questionDiv.querySelector('.explanation-group');

                console.log("Found elements:", {
                    wrongWordGroup: !!wrongWordGroup,
                    explanationGroup: !!explanationGroup
                });

                if (selectElement.value === 'false') {
                    if (wrongWordGroup) {
                        wrongWordGroup.style.display = '';
                        // Make sure the input is required when visible
                        const wrongWordInput = wrongWordGroup.querySelector('.wrong-word');
                        if (wrongWordInput) wrongWordInput.required = true;
                    }
                    if (explanationGroup) {
                        explanationGroup.style.display = '';
                        // Make sure the input is required when visible
                        const explanationInput = explanationGroup.querySelector('.explanation');
                        if (explanationInput) explanationInput.required = true;
                    }
                } else {
                    if (wrongWordGroup) {
                        wrongWordGroup.style.display = 'none';
                        // Remove required attribute when hidden
                        const wrongWordInput = wrongWordGroup.querySelector('.wrong-word');
                        if (wrongWordInput) wrongWordInput.required = false;
                    }
                    if (explanationGroup) {
                        explanationGroup.style.display = 'none';
                        // Remove required attribute when hidden
                        const explanationInput = explanationGroup.querySelector('.explanation');
                        if (explanationInput) explanationInput.required = false;
                    }
                }
            } catch (error) {
                console.error("Error in handleModifiedTrueFalseChange:", error);
            }
        }

        // Also define it as a regular function for backward compatibility
        function handleModifiedTrueFalseChange(selectElement) {
            window.handleModifiedTrueFalseChange(selectElement);
        }

        // Function to load and display student answers
        async function loadStudentAnswers(quizId, submissionId) {
            try {
                console.log(`Loading answers for quiz ${quizId}, submission ${submissionId}`);

                // Import Firestore functions dynamically
                const { doc, getDoc, collection } = await import("https://www.gstatic.com/firebasejs/10.11.1/firebase-firestore.js");

                // Make sure we have a Firestore instance
                if (!window.db) {
                    console.error("Firestore database instance is not available");
                    document.getElementById('student-answers-container').innerHTML =
                        '<div class="alert alert-danger">Error connecting to database. Please refresh the page.</div>';
                    return;
                }

                if (!quizId || !submissionId) {
                    document.getElementById('student-answers-container').innerHTML =
                        '<div class="alert alert-danger">Missing quiz ID or submission ID.</div>';
                    return;
                }

                // Get the quiz data (questions and correct answers)
                const quizDocRef = doc(window.db, 'Quizzes', quizId);
                const quizDoc = await getDoc(quizDocRef);
                if (!quizDoc.exists()) {
                    document.getElementById('student-answers-container').innerHTML =
                        '<div class="alert alert-danger">Quiz not found.</div>';
                    return;
                }

                const quizData = quizDoc.data();
                const questions = quizData.questions;

                // Get the student's submission
                const submissionDocRef = doc(window.db, 'QuizSubmissions', submissionId);
                const submissionDoc = await getDoc(submissionDocRef);
                if (!submissionDoc.exists()) {
                    document.getElementById('student-answers-container').innerHTML =
                        '<div class="alert alert-danger">Submission not found.</div>';
                    return;
                }

                const submission = submissionDoc.data();
                if (!submission.answers) {
                    document.getElementById('student-answers-container').innerHTML =
                        '<div class="alert alert-warning">No answers found in this submission.</div>';
                    return;
                }

                const studentAnswers = submission.answers;

                // Display the overall score at the top
                let scoreHtml = `
                    <div class="alert alert-info mb-4">
                        <h5 class="mb-2">Quiz Score</h5>
                        <p class="mb-0"><strong>Score:</strong> ${submission.score} out of ${submission.maxScore} (${submission.percentage.toFixed(1)}%)</p>
                    </div>
                `;

                // Create HTML to display questions, student answers, and correct answers
                let html = '<div class="student-answers-list">';

                // Add the score at the top
                html = scoreHtml + html;

                questions.forEach((question, index) => {
                    const studentAnswer = studentAnswers[index];
                    const isCorrect = checkIfAnswerIsCorrect(question, studentAnswer);

                    // Log detailed information about the answer checking
                    console.log(`Question ${index + 1} check:`, {
                        questionType: question.type,
                        questionText: question.text,
                        correctAnswer: question.correctAnswer,
                        studentAnswer: studentAnswer,
                        isCorrect: isCorrect
                    });

                    html += `
                        <div class="card mb-3">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">Question ${index + 1}</h6>
                                <span class="badge ${isCorrect ? 'badge-success' : 'badge-danger'} p-2">
                                    ${isCorrect ? 'Correct' : 'Incorrect'}
                                </span>
                            </div>
                            <div class="card-body">
                                <p class="question-text">${question.text}</p>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="answer-section">
                                            <h6 class="text-primary">Student's Answer:</h6>
                                            <div class="p-2 border rounded ${isCorrect ? 'bg-success text-white' : 'bg-danger text-white'}">
                                                ${formatStudentAnswer(question, studentAnswer)}
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="answer-section">
                                            <h6 class="text-primary">Correct Answer:</h6>
                                            <div class="p-2 border rounded bg-light">
                                                ${formatCorrectAnswer(question)}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;
                });

                html += '</div>';

                document.getElementById('student-answers-container').innerHTML = html;

            } catch (error) {
                console.error("Error loading student answers:", error);
                document.getElementById('student-answers-container').innerHTML = `
                    <div class="alert alert-danger">
                        <h5>Error loading answers</h5>
                        <p>${error.message || 'An unknown error occurred'}</p>
                        <button class="btn btn-sm btn-primary mt-2" onclick="$('#studentAnswersModal').modal('hide')">Close</button>
                        <button class="btn btn-sm btn-secondary mt-2" onclick="viewStudentAnswers('${quizId}', '${submissionId}', document.getElementById('studentAnswersModalLabel').textContent.split(' - ')[0], document.getElementById('studentAnswersModalLabel').textContent.split(' - ')[1])">Try Again</button>
                    </div>
                `;
            }
        }

        // Helper function to check if student answer is correct
        function checkIfAnswerIsCorrect(question, studentAnswer) {
            if (!studentAnswer) return false;

            console.log("Checking answer:", {
                questionType: question.type,
                correctAnswer: question.correctAnswer,
                studentAnswer: studentAnswer
            });

            // Normalize question type for consistent comparison
            const questionType = question.type.toLowerCase();

            if (questionType === 'multiplechoice') {
                // Handle both string and number formats for multiple choice answers
                // Convert both to strings for comparison to avoid type issues
                const studentChoice = String(studentAnswer);
                const correctChoice = String(question.correctAnswer);

                console.log('Multiple choice comparison:', {
                    studentChoice,
                    correctChoice,
                    match: studentChoice === correctChoice
                });

                return studentChoice === correctChoice;
            } else if (questionType === 'identification') {
                // Check if case sensitivity is enabled
                if (!studentAnswer || !question.correctAnswer) return false;

                if (question.caseSensitive) {
                    const isCorrect = studentAnswer.trim() === question.correctAnswer.trim();
                    console.log('Identification comparison (case sensitive):', {
                        studentAnswer: studentAnswer.trim(),
                        correctAnswer: question.correctAnswer.trim(),
                        isCorrect: isCorrect
                    });
                    return isCorrect;
                } else {
                    const isCorrect = studentAnswer.toLowerCase().trim() === question.correctAnswer.toLowerCase().trim();
                    console.log('Identification comparison (case insensitive):', {
                        studentAnswer: studentAnswer.toLowerCase().trim(),
                        correctAnswer: question.correctAnswer.toLowerCase().trim(),
                        isCorrect: isCorrect
                    });
                    return isCorrect;
                }
            } else if (questionType === 'truefalse') {
                // For regular true/false questions
                if (typeof studentAnswer === 'object' && studentAnswer.value) {
                    const isCorrect = studentAnswer.value === question.correctAnswer;
                    console.log('True/False comparison (object format):', {
                        studentAnswer: studentAnswer.value,
                        correctAnswer: question.correctAnswer,
                        isCorrect: isCorrect
                    });
                    return isCorrect;
                } else if (typeof studentAnswer === 'string') {
                    const isCorrect = studentAnswer === question.correctAnswer;
                    console.log('True/False comparison (string format):', {
                        studentAnswer: studentAnswer,
                        correctAnswer: question.correctAnswer,
                        isCorrect: isCorrect
                    });
                    return isCorrect;
                }
                return false;
            } else if (questionType === 'modifiedtruefalse') {
                if (typeof studentAnswer === 'object' && studentAnswer.value) {
                    // First check if true/false value is correct
                    if (studentAnswer.value !== question.correctAnswer) {
                        console.log('Modified True/False: T/F value incorrect');
                        return false;
                    }

                    // For modified true/false with false answer, check both the wrong word and the replacement word
                    if (question.correctAnswer === 'false') {
                        // Need both the wrong word and explanation to be provided
                        if (!studentAnswer.wrongWord || !studentAnswer.explanation || !question.wrongWord || !question.explanation) {
                            console.log("Missing required fields for modified true/false:", {
                                studentWrongWord: studentAnswer.wrongWord,
                                studentExplanation: studentAnswer.explanation,
                                questionWrongWord: question.wrongWord,
                                questionExplanation: question.explanation
                            });
                            return false;
                        }

                        // Case insensitive comparison for both words
                        const wrongWordMatches = studentAnswer.wrongWord.toLowerCase().trim() === question.wrongWord.toLowerCase().trim();
                        const explanationMatches = studentAnswer.explanation.toLowerCase().trim() === question.explanation.toLowerCase().trim();

                        console.log("Modified true/false comparison:", {
                            wrongWordMatches,
                            explanationMatches,
                            studentWrongWord: studentAnswer.wrongWord.toLowerCase().trim(),
                            questionWrongWord: question.wrongWord.toLowerCase().trim(),
                            studentExplanation: studentAnswer.explanation.toLowerCase().trim(),
                            questionExplanation: question.explanation.toLowerCase().trim()
                        });

                        // Both need to match to be correct
                        return wrongWordMatches && explanationMatches;
                    } else if (question.correctAnswer === 'true') {
                        console.log('Modified True/False: True answer is correct');
                        return true;
                    }

                    return false;
                } else if (typeof studentAnswer === 'string') {
                    // Handle simple string true/false answers
                    const isCorrect = studentAnswer === question.correctAnswer;
                    console.log('Modified True/False comparison (string format):', {
                        studentAnswer: studentAnswer,
                        correctAnswer: question.correctAnswer,
                        isCorrect: isCorrect
                    });
                    return isCorrect;
                }
            }

            // If we get here, the answer format doesn't match any expected format
            console.log("Answer format doesn't match expected format:", {
                questionType: question.type,
                studentAnswerType: typeof studentAnswer
            });

            // Last resort: try simple string comparison
            if (studentAnswer !== null && question.correctAnswer !== null) {
                const isCorrect = String(studentAnswer) === String(question.correctAnswer);
                console.log('Fallback string comparison:', {
                    studentAnswer: String(studentAnswer),
                    correctAnswer: String(question.correctAnswer),
                    isCorrect: isCorrect
                });
                return isCorrect;
            }

            return false;
        }

        // Helper function to format student answer for display
        function formatStudentAnswer(question, studentAnswer) {
            if (!studentAnswer) return '<em>No answer provided</em>';

            if (question.type === 'multipleChoice') {
                const choiceIndex = parseInt(studentAnswer);
                return question.choices[choiceIndex] || 'Invalid choice';
            } else if (question.type === 'identification') {
                return studentAnswer;
            } else if (question.type === 'trueFalse' || question.type === 'modifiedTrueFalse') {
                if (typeof studentAnswer === 'object') {
                    let answer = studentAnswer.value === 'true' ? 'True' : 'False';
                    if (studentAnswer.value === 'false') {
                        if (question.type === 'modifiedTrueFalse') {
                            if (studentAnswer.wrongWord) {
                                answer += `<br><strong>Wrong Word:</strong> ${studentAnswer.wrongWord}`;
                            }
                            if (studentAnswer.explanation) {
                                answer += `<br><strong>Replacement Word:</strong> ${studentAnswer.explanation}`;
                            }
                        } else if (studentAnswer.explanation) {
                            answer += `<br><strong>Explanation:</strong> ${studentAnswer.explanation}`;
                        }
                    }
                    return answer;
                } else {
                    return studentAnswer === 'true' ? 'True' : 'False';
                }
            }

            return String(studentAnswer);
        }

        // Helper function to format correct answer for display
        function formatCorrectAnswer(question) {
            if (question.type === 'multipleChoice') {
                return question.choices[question.correctAnswer] || 'Invalid choice';
            } else if (question.type === 'identification') {
                let answer = question.correctAnswer;
                // Add case sensitivity indicator
                if (question.caseSensitive) {
                    answer += ' <span class="badge badge-info">Case Sensitive</span>';
                } else {
                    answer += ' <span class="badge badge-secondary">Case Insensitive</span>';
                }
                return answer;
            } else if (question.type === 'trueFalse' || question.type === 'modifiedTrueFalse') {
                let answer = question.correctAnswer === 'true' ? 'True' : 'False';
                if (question.type === 'modifiedTrueFalse' && question.correctAnswer === 'false') {
                    answer += ' <span class="badge badge-secondary">Case Insensitive</span>';

                    if (question.wrongWord && question.explanation) {
                        answer += `<br><strong>Wrong Word:</strong> ${question.wrongWord}`;
                        answer += `<br><strong>Correct Word:</strong> ${question.explanation}`;
                    } else if (question.explanation) {
                        // For backward compatibility with older quizzes
                        answer += `<br><strong>Correct Word:</strong> ${question.explanation}`;
                    }
                }
                return answer;
            }

            return String(question.correctAnswer);
        }

        // Function to view student answers (called directly from button onclick)
        // Make this function available globally
        window.viewStudentAnswers = function(quizId, submissionId, studentName, quizTitle) {
            try {
                console.log("viewStudentAnswers called with:", { quizId, submissionId, studentName, quizTitle });

                // Update modal title
                document.getElementById('studentAnswersModalLabel').textContent =
                    `${studentName}'s Answers - ${quizTitle}`;

                // Show loading state
                document.getElementById('student-answers-container').innerHTML = `
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status"></div>
                        <p>Loading answers...</p>
                    </div>
                `;

                // Show the modal
                $('#studentAnswersModal').modal('show');

                // Load the student's answers
                loadStudentAnswers(quizId, submissionId);
            } catch (error) {
                console.error("Error in viewStudentAnswers:", error);
                alert("Error viewing student answers: " + (error.message || "Unknown error"));
            }
        }

        // Also define it as a regular function for backward compatibility
        function viewStudentAnswers(quizId, submissionId, studentName, quizTitle) {
            window.viewStudentAnswers(quizId, submissionId, studentName, quizTitle);
        }

        // We don't need the original setupViewAnswersButtons function anymore
        // since we're using direct onclick handlers
    </script>



    <style>
        /* Classroom List Styles */
        .classroom-item {
            border: 1px solid #e3e6f0;
            transition: all 0.2s ease;
        }

        .classroom-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 .5rem 1rem rgba(0,0,0,.15)!important;
        }

        .classroom-item h5 {
            color: #333;
            font-size: 1.1rem;
        }

        .classroom-item .btn-outline-primary {
            color: #4e73df;
            border-color: #4e73df;
        }

        .classroom-item .btn-outline-primary:hover {
            background-color: #4e73df;
            color: white;
        }

        /* Profile Dropdown Styles */
        .dropdown-header {
            padding: 1rem;
            text-align: center;
        }

        .profile-info {
            margin-top: 0.5rem;
        }

        .dropdown-item {
            padding: 0.5rem 1rem;
        }

        .dropdown-item i {
            width: 20px;
            text-align: center;
        }

        /* Sidebar Toggle Hover Style */
        #sidebarToggle .nav-link:hover {
            color: #105c1c !important;
        }

        /* Profile Modal Styles */
        .modal-body {
            padding: 2rem;
        }

        .card {
            margin-bottom: 1.5rem;
        }

        .card-header {
            background-color: #f8f9fc;
            border-bottom: 1px solid #e3e6f0;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .modal-dialog {
                margin: 0.5rem;
            }
        }

        /* Quiz Creation Modal Styles */
        .question-item {
            background-color: #f8f9fc;
            position: relative;
        }

        .remove-question {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }

        .remove-question:hover {
            background-color: #dc3545;
            border-color: #dc3545;
        }

        .choices-container {
            margin-top: 10px;
        }

        .input-group-text {
            background-color: transparent;
        }

        .input-group-text input[type="radio"] {
            margin-right: 5px;
        }

        /* Quiz List Styles */
        .card {
            transition: transform 0.2s;
        }

        .card:hover {
            transform: translateY(-2px);
        }

        .question-item {
            background-color: #f8f9fc;
        }

        .choices-container {
            margin-top: 10px;
        }

        .form-check {
            margin-bottom: 5px;
        }

        .form-check-input:disabled {
            background-color: #e9ecef;
        }

        /* Text color styles */
        .text-dark {
            color: #000000 !important;
        }

        .modal-title {
            color: #000000;
        }

        .form-label {
            color: #000000;
        }

        .question-item h6 {
            color: #000000;
        }

        .form-control {
            color: #000000;
        }

        .form-control::placeholder {
            color: #6c757d;
        }

        /* Student Answers Modal Styles */
        .student-answers-list {
            max-height: 70vh;
            overflow-y: auto;
        }

        .question-text {
            font-weight: 600;
            margin-bottom: 15px;
            color: #000000;
        }

        .answer-section {
            margin-bottom: 10px;
        }

        /* Copy Quiz Code Button Styles */
        .copy-quiz-code-btn {
            color: #6c757d;
            transition: all 0.2s ease;
            font-size: 0.875rem;
        }

        .copy-quiz-code-btn:hover {
            color: #007bff;
            transform: scale(1.1);
        }

        .copy-quiz-code-btn:focus {
            outline: none;
            box-shadow: none;
        }

        .answer-section h6 {
            margin-bottom: 5px;
            color: #000000;
        }

        .bg-success.text-white {
            background-color: #28a745 !important;
        }

        .bg-danger.text-white {
            background-color: #dc3545 !important;
        }

        .bg-light {
            color: #000000;
        }
    </style>

    <!-- Student Answers Modal -->
    <div class="modal fade" id="studentAnswersModal" tabindex="-1" role="dialog" aria-labelledby="studentAnswersModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="studentAnswersModalLabel">Student Answers</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="student-answers-container">
                        <div class="text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p>Loading answers...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- File Preview Modal -->
    <div class="modal fade" id="filePreviewModal" tabindex="-1" role="dialog" aria-labelledby="filePreviewModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="filePreviewModalLabel">File Preview</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="filePreviewContent">
                        <div class="file-preview-loading text-center">
                            <div class="spinner-border text-primary" role="status"></div>
                            <p>Loading file preview...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <a href="#" class="btn btn-primary" id="downloadFileBtn" download>
                        <i class="fas fa-download"></i> Download File
                    </a>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Responsive JavaScript -->
    <script src="js/mobile-responsive.js"></script>

    <!-- Mobile Sidebar Enhancement Script -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Debug mobile sidebar functionality
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggleTop = document.getElementById('sidebarToggleTop');
            const mobileCloseBtn = document.getElementById('mobileSidebarClose');

            console.log('Debug - Mobile sidebar elements:');
            console.log('- Sidebar:', sidebar);
            console.log('- Toggle button:', sidebarToggleTop);
            console.log('- Close button:', mobileCloseBtn);
            console.log('- MobileUtils available:', typeof window.MobileUtils);

            if (window.MobileUtils) {
                console.log('- MobileUtils.toggleSidebar:', typeof window.MobileUtils.toggleSidebar);
                console.log('- MobileUtils.closeSidebar:', typeof window.MobileUtils.closeSidebar);
            }

            // Add mobile close button functionality
            if (mobileCloseBtn) {
                mobileCloseBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    console.log('Mobile close button clicked');

                    // Use jQuery to trigger the same event as sb-admin-2.js
                    if (typeof $ !== 'undefined') {
                        $('#sidebarToggleTop').trigger('click');
                    } else {
                        // Fallback: manually add the classes to hide sidebar
                        if (sidebar) {
                            sidebar.classList.add('toggled');
                            document.body.classList.add('sidebar-toggled');
                        }
                    }
                });
            }
        });
    </script>

</body>

</html>